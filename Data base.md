### Excel File Structure with TypeScript Data Types**

This document outlines the column structure, row positioning, and recommended data types for each field. Using `| null` is crucial to handle empty cells or errors like `#DIV/0!` gracefully.

| Column Letter | Row Position | Header Text                                          | Suitable TypeScript Data Type       | Notes                            |
| :------------ | :----------- | :--------------------------------------------------- | :---------------------------------- | :------------------------------- |
| A             | 1            | #                                                    | `number`                            |                                  |
| B             | 1            | Company                                              | `string`                            |                                  |
| C             | 1            | Company code                                         | `string`                            | Alphanumeric code                |
| D             | 2            | **Weekly update**                                    | (container object)                  | Spans D-F                        |
|               | 3            | Current market price                                 | `number \| null`                    |                                  |
| E             | 3            | Total Market Cap                                     | `number \| null`                    |                                  |
| F             | 3            | BETA VALUES AGAINST ASPI                             | `number \| null`                    |                                  |
| G             | 1            | BETA VALUES AGAINST S&P                              | `number \| null`                    |                                  |
| H             | 1            | GICS Industry Group                                  | `string \| null`                    |                                  |
| I             | 2            | GICS Summary                                         | `string \| null`                    |                                  |
| J             | 1            | Sector                                               | `string \| null`                    |                                  |
| K             | 1            | QUOTED year                                          | `number \| null`                    |                                  |
| L             | 1            | QUOTED DATE                                          | `string \| null`                    | Can be parsed to a Date object   |
| M             | 1            | Finance year                                         | `string \| null`                    |                                  |
| N             | 1            | Owner                                                | `string \| null`                    |                                  |
| O             | 1            | Owner Rating                                         | `string \| number \| null`          |                                  |
| P             | 1            | Chairman                                             | `string \| null`                    |                                  |
| Q             | 1            | CEO                                                  | `string \| null`                    |                                  |
| R             | 1            | MD                                                   | `string \| null`                    |                                  |
| S             | 1            | Main shareholders                                    | `string \| null`                    |                                  |
| T             | 1            | Main shareholders rating                             | `string \| number \| null`          |                                  |
| U             | 1            | Main shareholders %                                  | `number \| null`                    |                                  |
| V             | 1            | Public share holdings %                              | `number \| null`                    |                                  |
| W-AH          | 2-4          | **Actual Cash Dividend 2024**                        | `{[month: string]: number \| null}` | Object with month keys           |
| W             | 4            | Jan                                                  | `number \| null`                    |                                  |
| X             | 4            | Feb                                                  | `number \| null`                    |                                  |
| ...           | 4            | ...                                                  | `number \| null`                    | (Mar-Dec)                        |
| AI-AO         | 2-4          | **Actual Cash Dividend 2025**                        | `{[month: string]: number \| null}` | Object with month keys           |
| AI            | 4            | Jan                                                  | `number \| null`                    |                                  |
| ...           | 4            | ...                                                  | `number \| null`                    | (Feb-Jul)                        |
| AP            | 1            | Total Cash Dividend amount                           | `number \| null`                    |                                  |
| AQ            | 1            | Dividend Yield                                       | `number \| null`                    | Stored as a number (e.g., 0.078) |
| AR            | 1            | Dividend Rating                                      | `string \| number \| null`          |                                  |
| AS            | 1            | Net asset per share                                  | `number \| null`                    |                                  |
| AT            | 2-3          | Earning Per share (Annual)                           | `number \| null`                    |                                  |
| AU            | 1            | PBV                                                  | `number \| null`                    |                                  |
| AV            | 2-3          | PE / Growth Potential (PBV)                          | `number \| null`                    |                                  |
| AW            | 1            | PE Rating                                            | `string \| number \| null`          |                                  |
| AX            | 1            | Sum of sector price                                  | `number \| null`                    |                                  |
| AY            | 1            | Sum of sector Earnings                               | `number \| null`                    |                                  |
| AZ            | 1            | Industry PE%                                         | `number \| null`                    |                                  |
| BA-BD         | 2-3          | **Newly updating**                                   | (container object)                  | Spans BA-BD                      |
| BA            | 3            | ROE                                                  | `number \| null`                    |                                  |
| BB            | 3            | ROE Rating                                           | `string \| number \| null`          |                                  |
| BC            | 3            | ROA                                                  | `number \| null`                    |                                  |
| BD            | 3            | ROA Rating                                           | `string \| number \| null`          |                                  |
| BE            | 1            | Net Income,...                                       | `number \| null`                    |                                  |
| BF            | 1            | Total Layability                                     | `number \| null`                    | Likely typo for "Liability"      |
| BG            | -            | *(Undefined/Conflicting)*                            | `unknown`                           |                                  |
| BH            | 1            | Total Equity                                         | `number \| null`                    |                                  |
| BI            | 1            | Total Assert                                         | `number \| null`                    | Likely typo for "Asset"          |
| BJ            | 1            | Debt to Equity (Giyaring)                            | `number \| null`                    |                                  |
| BK            | 1            | Debt to Equity (Giyaring) Rating                     | `string \| number \| null`          |                                  |
| BL            | 1            | Debt reatio                                          | `number \| null`                    | Likely typo for "ratio"          |
| BM            | 1            | Debt reatio Rating                                   | `string \| number \| null`          | Likely typo for "ratio"          |
| BN            | 1            | Revenue Growth 2024                                  | `number \| null`                    |                                  |
| BO            | 1            | Revenue Growth 2023                                  | `number \| null`                    |                                  |
| BP            | 1            | Average Growth Revenue/Sales/ Gross written premiums | `number \| null`                    |                                  |
| BQ            | -            | *(Empty Header)*                                     | `unknown`                           | Column is empty                  |
| BR            | 2            | Average Revenue/Sales/ Gross written premiums        | `number \| null`                    |                                  |
