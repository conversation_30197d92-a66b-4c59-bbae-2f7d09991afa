<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a19e36fa-e45b-4eb8-8cf5-116ecf4c5ecf" name="Changes" comment="&#10;- Display proper stats in the header with pagination details&#10;- Handle no data state with descriptive messages">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/DataTable.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/DataTable.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;nirzaf&quot;
  }
}</component>
  <component name="GithubDefaultAccount">
    <option name="defaultAccountId" value="9b5884cd-c155-4fee-ba20-87b8b32cc541" />
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/nirzaf/exceldb.git&quot;,
    &quot;accountId&quot;: &quot;9b5884cd-c155-4fee-ba20-87b8b32cc541&quot;
  }
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2ydc1Gtr1mjTetwAq67saDPEvRA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "js.debugger.nextJs.config.created.client": "true",
    "js.debugger.nextJs.config.created.server": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.Next.js: server-side.executor": "Run",
    "settings.editor.selected.configurable": "org.jetbrains.plugins.github.ui.GithubSettingsConfigurable",
    "settings.editor.splitter.proportion": "0.32484725",
    "ts.external.directory.path": "/Users/<USER>/Repos/exceldashboard/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a19e36fa-e45b-4eb8-8cf5-116ecf4c5ecf" name="Changes" comment="" />
      <created>1750167674266</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750167674266</updated>
      <workItem from="1750167678382" duration="201000" />
      <workItem from="1750170678918" duration="940000" />
      <workItem from="1750172078261" duration="5000" />
      <workItem from="1750172096374" duration="1261000" />
      <workItem from="1750177233557" duration="1270000" />
      <workItem from="1750265807718" duration="2974000" />
    </task>
    <task id="LOCAL-00001" summary="chore: add IntelliJ IDE configuration files to .gitignore and initialize .idea metadata files">
      <option name="closed" value="true" />
      <created>1750167701186</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750167701186</updated>
    </task>
    <task id="LOCAL-00002" summary="feat(DataTable): add pagination and enhance filtering&#10;&#10;- Implement global and local pagination with page state persistence&#10;- Add pagination controls and page navigation functionality&#10;- Enhance filtering to hide rows 2-4 instead of 2-3&#10;- Reset to first page on data, sorting, or search changes">
      <option name="closed" value="true" />
      <created>1750170812816</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750170812816</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="chore: add IntelliJ IDE configuration files to .gitignore and initialize .idea metadata files" />
    <MESSAGE value="feat(DataTable): add pagination and enhance filtering&#10;&#10;- Implement global and local pagination with page state persistence&#10;- Add pagination controls and page navigation functionality&#10;- Enhance filtering to hide rows 2-4 instead of 2-3&#10;- Reset to first page on data, sorting" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(DataTable): add pagination and enhance filtering&#10;&#10;- Implement global and local pagination with page state persistence&#10;- Add pagination controls and page navigation functionality&#10;- Enhance filtering to hide rows 2-4 instead of 2-3&#10;- Reset to first page on data, sorting" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>