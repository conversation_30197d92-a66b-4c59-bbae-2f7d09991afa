"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export interface SheetData {
  [key: string]: any[]
}

export interface DataContextType {
  data: SheetData | null
  setData: (data: SheetData | null) => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  error: string | null
  setError: (error: string | null) => void
  fileName: string | null
  setFileName: (fileName: string | null) => void
  fileDate: string | null
  setFileDate: (fileDate: string | null) => void
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export function DataProvider({ children }: { children: ReactNode }) {
  const [data, setData] = useState<SheetData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const [fileDate, setFileDate] = useState<string | null>(null)

  // Load data from localStorage on component mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('excel-dashboard-data')
      const savedFileName = localStorage.getItem('excel-dashboard-filename')
      const savedFileDate = localStorage.getItem('excel-dashboard-filedate')
      
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        setData(parsedData)
      }
      
      if (savedFileName) {
        setFileName(savedFileName)
      }
      
      if (savedFileDate) {
        setFileDate(savedFileDate)
      }
    } catch (error) {
      console.error('Failed to load data from localStorage:', error)
      // Clear corrupted data
      localStorage.removeItem('excel-dashboard-data')
      localStorage.removeItem('excel-dashboard-filename')
      localStorage.removeItem('excel-dashboard-filedate')
    }
  }, [])

  // Enhanced setData function that also saves to localStorage
  const setDataWithStorage = (newData: SheetData | null) => {
    setData(newData)
    try {
      if (newData) {
        // Save data to localStorage when new data is set
        localStorage.setItem('excel-dashboard-data', JSON.stringify(newData))
      } else {
        // Remove data from localStorage when data is cleared
        localStorage.removeItem('excel-dashboard-data')
        localStorage.removeItem('excel-dashboard-filename')
        localStorage.removeItem('excel-dashboard-filedate')
        setFileName(null)
        setFileDate(null)
      }
    } catch (error) {
      console.error('Failed to save data to localStorage:', error)
    }
  }

  // Enhanced setFileName function that also saves to localStorage
  const setFileNameWithStorage = (newFileName: string | null) => {
    setFileName(newFileName)
    try {
      if (newFileName) {
        localStorage.setItem('excel-dashboard-filename', newFileName)
      } else {
        localStorage.removeItem('excel-dashboard-filename')
      }
    } catch (error) {
      console.error('Failed to save filename to localStorage:', error)
    }
  }

  // Enhanced setFileDate function that also saves to localStorage
  const setFileDateWithStorage = (newFileDate: string | null) => {
    setFileDate(newFileDate)
    try {
      if (newFileDate) {
        localStorage.setItem('excel-dashboard-filedate', newFileDate)
      } else {
        localStorage.removeItem('excel-dashboard-filedate')
      }
    } catch (error) {
      console.error('Failed to save file date to localStorage:', error)
    }
  }

  return (
    <DataContext.Provider
      value={{
        data,
        setData: setDataWithStorage,
        isLoading,
        setIsLoading,
        error,
        setError,
        fileName,
        setFileName: setFileNameWithStorage,
        fileDate,
        setFileDate: setFileDateWithStorage,
      }}
    >
      {children}
    </DataContext.Provider>
  )
}

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider")
  }
  return context
}
