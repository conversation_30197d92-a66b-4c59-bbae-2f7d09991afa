// types/database.ts

/**
 * Represents a single row of cleaned and structured data from the 'Data base' Excel sheet.
 * Contains known common fields, specific financial metrics, and allows for dynamically generated fields.
 */
export interface CleanedDatabaseData {
  // Common identifiable fields (types based on typical usage)
  '#': number | string | null; // Usually a row number or ID
  'Company': string | null;
  'Company code': string | null;

  // Fields from the 'Weekly update' section (examples)
  'Current price'?: number | null;
  'Total Market Cap'?: number | null;
  'BETA VALUES AGAINST ASPI'?: number | null;

  // Specifically requested financial metrics with years
  'Net asset per share 2024'?: number | null;
  'EPS 2024'?: number | null;
  'Cash Dividend 2024'?: number | null;

  // Other financial data examples for historical trends
  'Revenue/Sales/Gross written premiums 2019'?: number | null;
  'Revenue/Sales/Gross written premiums 2020'?: number | null;
  'Revenue/Sales/Gross written premiums 2021'?: number | null;
  'Revenue/Sales/Gross written premiums 2022'?: number | null;
  'Revenue/Sales/Gross written premiums 2023'?: number | null;
  'Revenue/Sales/Gross written premiums 2024'?: number | null;

  'EPS 2019'?: number | null;
  'EPS 2020'?: number | null;
  'EPS 2021'?: number | null;
  'EPS 2022'?: number | null;
  'EPS 2023'?: number | null;

  'Net asset per share 2019'?: number | null;
  'Net asset per share 2020'?: number | null;
  'Net asset per share 2021'?: number | null;
  'Net asset per share 2022'?: number | null;
  'Net asset per share 2023'?: number | null;

  'Cash Dividend 2019'?: number | null;
  'Cash Dividend 2020'?: number | null;
  'Cash Dividend 2021'?: number | null;
  'Cash Dividend 2022'?: number | null;
  'Cash Dividend 2023'?: number | null;

  'ROE'?: number | null; // Return on Equity
  'ROA'?: number | null; // Return on Asset
  'Debt to Equity (Giyaring)'?: number | null;

  // Index signature to allow for any other dynamically generated string keys.
  // Values can be numbers, strings, or null after processing.
  [key: string]: any;
}
