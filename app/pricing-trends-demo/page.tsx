import { PricingTrendsDemo } from "@/components/PricingTrendsDemo"

export default function PricingTrendsDemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/20 to-green-50/20 dark:via-blue-950/20 dark:to-green-950/20">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-primary via-accent to-info bg-clip-text text-transparent">
            Enhanced Pricing Trends Demo
          </h1>
          <p className="text-muted-foreground text-lg">
            Interactive demonstration of the pricing trends analysis feature
          </p>
        </div>
        
        <PricingTrendsDemo />
        
        <div className="mt-8 text-center">
          <div className="max-w-2xl mx-auto bg-card border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">About This Demo</h2>
            <div className="text-left space-y-2 text-sm text-muted-foreground">
              <p>• This demo shows how the Enhanced Pricing Trends feature works with sample banking company data</p>
              <p>• In the real application, data is fetched from MongoDB containing uploaded Excel files</p>
              <p>• Users can select multiple companies and view their price trends over time</p>
              <p>• The feature supports up to 8 companies and analyzes recent 15 database records</p>
              <p>• Charts are interactive with tooltips, legends, and responsive design</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
