import { GoogleGenerativeAI } from '@google/generative-ai'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { prompt, companyCode, companyData } = await request.json()

    console.log("AI Analysis request received:", { companyCode, hasCompanyData: !!companyData })

    if (!process.env.GEMINI_API_KEY) {
      console.error("Gemini API key not configured")
      return NextResponse.json({ error: "Gemini API key not configured" }, { status: 500 })
    }

    const companyName = companyData?.database?.["Company Name"] || companyCode || "Unknown Company"

    // Check if this is a summary request (short prompt for company profile)
    const isSummaryRequest = prompt.includes("30-40 words") || prompt.includes("concise company summary")

    let fullPrompt = ""

    if (isSummaryRequest) {
      // For summary requests, use minimal data and focused prompt
      const sector = companyData?.database?.["Sector"] || "N/A"
      const marketCapCategory = companyData?.database?.["Market Cap Category"] || "N/A"

      fullPrompt = `Generate a concise professional summary for ${companyName} in exactly 30-40 words.

Company Information:
- Name: ${companyName}
- Sector: ${sector}
- Market Cap Category: ${marketCapCategory}

Requirements:
- Exactly 30-40 words
- Professional tone
- Focus on business nature and sector
- No markdown formatting
- Plain text only
- Start with the company's primary business activity`
    } else {
      // For comprehensive analysis requests, use full data
      const dataString = JSON.stringify(companyData, null, 2)

      fullPrompt = `Analyze ${companyName} (${companyCode}) based on: ${prompt}

Company Data:
${dataString}

Please provide a comprehensive analysis focusing on key insights, financial health, and investment perspective.

IMPORTANT: Format your response in clean, well-structured Markdown with:
- Use **bold** for important metrics and headings
- Use *italics* for emphasis
- Use bullet points and numbered lists for clarity
- Use proper headings (##, ###) to organize sections
- Include line breaks for readability
- Make it visually appealing and easy to read

Keep the response concise but well-formatted.`
    }

    console.log("Sending request to Gemini API for", isSummaryRequest ? "summary" : "analysis", "...")

    // Initialize Gemini AI using the library approach with fallback
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)

    let analysis = ""
    let modelUsed = ""

    // Try primary model first, then fallback
    const modelsToTry = isSummaryRequest
      ? ["gemini-1.5-flash", "gemini-2.5-flash-preview-05-20"]
      : ["gemini-2.5-flash-preview-05-20", "gemini-1.5-flash"]

    for (const modelName of modelsToTry) {
      try {
        const model = genAI.getGenerativeModel({
          model: modelName,
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: isSummaryRequest ? 200 : 16384,
          }
        })

        const result = await model.generateContent(fullPrompt)
        const response = await result.response
        analysis = response.text()
        modelUsed = modelName

        if (analysis && analysis.trim() !== "") {
          break // Success, exit the loop
        }
      } catch (error: any) {
        console.log(`Model ${modelName} failed:`, error.message)
        if (modelName === modelsToTry[modelsToTry.length - 1]) {
          // This was the last model to try, re-throw the error
          throw error
        }
        // Continue to next model
      }
    }

    console.log("Gemini AI response received successfully for", isSummaryRequest ? "summary" : "analysis", "using", modelUsed)

    if (!analysis || analysis.trim() === "") {
      if (isSummaryRequest) {
        // For summary requests, provide a fallback summary
        const companyName = companyData?.database?.["Company Name"] || companyCode || "Unknown Company"
        const sector = companyData?.database?.["Sector"] || "business"
        const fallbackSummary = `${companyName} operates in the ${sector.toLowerCase()} sector, providing specialized services and solutions to customers and stakeholders in their market segment.`
        console.log("Using fallback summary due to empty AI response")
        return NextResponse.json({ analysis: fallbackSummary })
      } else {
        throw new Error("Empty response from Gemini API")
      }
    }

    return NextResponse.json({ analysis: analysis.trim() })
  } catch (error) {
    console.error("AI Analysis error:", error)

    // Provide user-friendly error messages
    let userFriendlyMessage = "We're having trouble generating your analysis right now."
    let actionableAdvice = "Please try again in a few moments."
    
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase()
      
      if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
        userFriendlyMessage = "Our AI service is currently experiencing high demand."
        actionableAdvice = "Please wait 30 seconds and try again."
      } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        userFriendlyMessage = "Connection to our AI service timed out."
        actionableAdvice = "Please check your internet connection and try again."
      } else if (errorMessage.includes('unauthorized') || errorMessage.includes('api key')) {
        userFriendlyMessage = "There's an issue with our AI service configuration."
        actionableAdvice = "Please contact support if this problem persists."
      } else if (errorMessage.includes('invalid') || errorMessage.includes('bad request')) {
        userFriendlyMessage = "The analysis request couldn't be processed."
        actionableAdvice = "Please try selecting different analysis options."
      }
    }

    return NextResponse.json(
      {
        error: userFriendlyMessage,
        action: actionableAdvice,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
