import { NextResponse } from 'next/server'
import { getCollection } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Debug MongoDB data structure and sizes
 * GET /api/debug-data
 */
export async function GET() {
  try {
    console.log('Debugging MongoDB data structure...')
    
    const collection = await getCollection('excel_uploads')
    
    // Get basic stats
    const totalCount = await collection.countDocuments()
    console.log('Total documents:', totalCount)
    
    // Get document sizes and structure info
    const pipeline = [
      {
        $project: {
          filename: 1,
          uploadedAt: 1,
          dataSize: { $bsonSize: "$data" },
          hasDatabase: { $ne: ["$data.database", null] },
          databaseCount: { 
            $cond: {
              if: { $isArray: "$data.database" },
              then: { $size: "$data.database" },
              else: 0
            }
          }
        }
      },
      { $sort: { uploadedAt: -1 } },
      { $limit: 10 }
    ]
    
    const stats = await collection.aggregate(pipeline).toArray()
    
    console.log('Document stats:', stats)
    
    return NextResponse.json({
      success: true,
      totalDocuments: totalCount,
      documentStats: stats.map(doc => ({
        _id: doc._id?.toString(),
        filename: doc.filename,
        uploadedAt: doc.uploadedAt?.toISOString(),
        dataSizeBytes: doc.dataSize,
        dataSizeMB: Math.round(doc.dataSize / 1024 / 1024 * 100) / 100,
        hasDatabase: doc.hasDatabase,
        databaseRecordCount: doc.databaseCount
      })),
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Debug data failed:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json(
      {
        success: false,
        error: 'Debug failed',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
