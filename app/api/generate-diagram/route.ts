import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { prompt, companyCode, companyData } = await request.json()

    console.log("Diagram generation request received:", { companyCode, hasCompanyData: !!companyData })

    if (!process.env.GEMINI_API_KEY) {
      console.error("Gemini API key not configured")
      return NextResponse.json({ error: "Gemini API key not configured" }, { status: 500 })
    }

    const companyName = companyData?.database?.["Company Name"] || companyCode || "Unknown Company"

    // Analyze available data to provide better context to AI
    const availableDataTypes = [];
    if (companyData?.database) availableDataTypes.push("Financial Database");
    if (companyData?.dailyPrice) availableDataTypes.push("Daily Price Data");
    if (companyData?.marketCap) availableDataTypes.push("Market Cap Data");

    // Extract key financial metrics for better AI understanding
    const keyMetrics = {};
    const importantFields = [
      "Current price", "Net Asset per share", "Earning per share (Annual)", 
      "Total Cash Dividend amount", "ROE", "ROA", "PBV", "PE", 
      "BETA VALUES AGAINST ASPI", "Debt to Equity (Giyaring)", 
      "Dividend yield", "Market cap/ Total Market cap",
      "2024 Revenue/Sales/Gross written premiums", "2023 Revenue/Sales/Gross written premiums",
      "2022 Revenue/Sales/Gross written premiums", "2021 Revenue/Sales/Gross written premiums",
      "2020 Revenue/Sales/Gross written premiums", "Q1N", "Q2N", "Q3N", "Q4N",
      "Q1E", "Q2E", "Q3E", "Q4E", "Revenue Growth 2024", "Revenue Growth 2023"
    ];
    
    if (companyData?.database) {
      const db = companyData.database;
      // Extract important financial metrics
      importantFields.forEach(field => {
        const value = db[field];
        if (value !== null && value !== undefined && value !== "" && value !== "#REF!" && value !== "#VALUE!") {
          const numericValue = parseFloat(String(value).replace(/[^\d.-]/g, ""));
          if (!isNaN(numericValue)) {
            keyMetrics[field] = numericValue;
          }
        }
      });
      
      // Also include company info
      if (db["Company"]) keyMetrics["Company"] = db["Company"];
      if (db["Sector"]) keyMetrics["Sector"] = db["Sector"];
      if (db["BUSINESS SUMMARY"]) keyMetrics["Business Summary"] = db["BUSINESS SUMMARY"];
    }

    const aiPrompt = `
You are a financial data visualization expert. Based on the user's request and the provided company data, generate appropriate chart data.

User Request: "${prompt}"
Company: ${companyData?.database?.Company || 'Unknown'}
Symbol: ${companyData?.database?.Symbol || 'Unknown'}

IMPORTANT DATA STRUCTURE:
The data comes from a financial database with these key fields:
- Current price: Current stock price
- Net Asset per share: Book value per share
- Earning per share (Annual): EPS
- ROE, ROA, PBV, PE: Financial ratios
- Revenue data: "2024 Revenue/Sales/Gross written premiums", "2023 Revenue/Sales/Gross written premiums", etc.
- Quarterly data: Q1N, Q2N, Q3N, Q4N (Net), Q1E, Q2E, Q3E, Q4E (Earnings)
- Growth metrics: "Revenue Growth 2024", "Revenue Growth 2023"
- Market metrics: "Market cap/ Total Market cap", "BETA VALUES AGAINST ASPI"
- Dividend info: "Total Cash Dividend amount", "Dividend yield"

Key Financial Metrics Available:
${Object.entries(keyMetrics).map(([key, value]) => `- ${key}: ${value}`).join('\n')}

For trend charts, use:
- Revenue data across years (2020-2024)
- Quarterly progression (Q1-Q4)
- Growth metrics over time
- Price vs Net Asset value trends

For performance charts, use:
- Financial ratios (ROE, ROA, PE, PBV)
- Market metrics (Beta, Market cap ratio)
- Profitability metrics (EPS, Dividend yield)

Generate a JSON response with the following structure:
{
  "chartType": "line" | "bar" | "pie",
  "title": "Chart Title",
  "data": [
    {"name": "Label", "value": number} // for bar/pie charts
    // OR
    {"name": "Label", "price": number} // for line charts
  ]
}

Guidelines:
1. For "price trend" or "trend" requests, use line charts showing revenue progression or quarterly data
2. Use actual field names from the data structure
3. Filter out invalid data (#REF!, #VALUE!, empty strings, null)
4. For revenue trends, use the "Revenue/Sales/Gross written premiums" fields
5. For quarterly trends, use Q1N-Q4N or Q1E-Q4E fields
6. Ensure meaningful, accurate chart titles
7. Include only valid numeric data points

Respond with valid JSON only.`;

    console.log("Sending request to Gemini API for diagram...")

    // Create AbortController for timeout handling
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 60000) // 60 second timeout

    let aiResponse
    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${process.env.GEMINI_API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          signal: controller.signal,
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: aiPrompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: 0.3,
              topK: 20,
              topP: 0.8,
              maxOutputTokens: 1024,
            },
          }),
        },
      )

      clearTimeout(timeoutId)
      
      console.log("Gemini API response status:", response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Gemini API error:", errorText)
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log("Gemini API response received successfully")

      aiResponse = result.candidates?.[0]?.content?.parts?.[0]?.text || "{}"
    } catch (fetchError) {
      clearTimeout(timeoutId)
      
      if (fetchError.name === 'AbortError') {
        console.error("Request timed out after 60 seconds")
        throw new Error("Request timed out. The AI service is taking longer than expected to respond.")
      }
      
      throw fetchError
    }

    try {
      // Try to extract JSON from the response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
      const jsonString = jsonMatch ? jsonMatch[0] : aiResponse
      const chartData = JSON.parse(jsonString)

      return NextResponse.json(chartData)
    } catch (parseError) {
      console.log("Failed to parse AI response, using fallback")

      // Fallback with company-specific data
      const fallbackData = []

      if (companyData?.dailyPrice) {
        const dailyPrice = companyData.dailyPrice
        fallbackData.push(
          {
            name: "Open",
            value: Number.parseFloat(String(dailyPrice["Open (Rs.)"]).replace(/[^\d.-]/g, "")) || 0,
          },
          {
            name: "High",
            value: Number.parseFloat(String(dailyPrice["High (Rs.)"]).replace(/[^\d.-]/g, "")) || 0,
          },
          {
            name: "Low",
            value: Number.parseFloat(String(dailyPrice["Low (Rs.)"]).replace(/[^\d.-]/g, "")) || 0,
          },
          {
            name: "Close",
            value: Number.parseFloat(String(dailyPrice["Last Trade (Rs.)"]).replace(/[^\d.-]/g, "")) || 0,
          },
        )
      }

      return NextResponse.json({
        data: fallbackData.filter((item) => item.value > 0),
        config: {
          type: "bar",
          title: `${companyName} - Performance Analysis`,
        },
      })
    }
  } catch (error) {
    console.error("Diagram generation error:", error)

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"

    return NextResponse.json(
      {
        error: "Failed to generate diagram",
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
