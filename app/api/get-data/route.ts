import { NextRequest, NextResponse } from 'next/server'
import { getUploadedFiles, getUploadedFileById } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get uploaded Excel data from MongoDB
 * GET /api/get-data?id=<document_id> - Get specific document
 * GET /api/get-data?limit=10&skip=0 - Get list of documents
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = parseInt(searchParams.get('skip') || '0')

    if (id) {
      // Get specific document by ID
      console.log('Fetching document by ID:', id)
      
      const document = await getUploadedFileById(id)
      
      if (!document) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        document
      })
    } else {
      // Get list of documents
      console.log('Fetching documents list:', { limit, skip })

      // Temporarily disable MongoDB list queries due to large documents causing memory issues
      console.log('MongoDB list queries temporarily disabled due to memory constraints')
      return NextResponse.json({
        success: false,
        documents: [],
        count: 0,
        error: 'Database temporarily unavailable',
        details: 'Large documents causing memory issues. Please use file upload instead.',
        timestamp: new Date().toISOString()
      })
    }
  } catch (error) {
    console.error('Error fetching data from MongoDB:', error)

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

    // Check if it's a connection error
    if (errorMessage.includes('connection') || errorMessage.includes('timeout')) {
      return NextResponse.json({
        success: false,
        documents: [],
        count: 0,
        error: 'Database connection failed',
        details: errorMessage,
        timestamp: new Date().toISOString()
      })
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch data',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}