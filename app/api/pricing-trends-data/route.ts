import { NextRequest, NextResponse } from 'next/server'
import { getRecentDatabaseRecords } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get recent database records for pricing trends analysis
 * GET /api/pricing-trends-data?limit=15 - Get recent database records
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '15')
    const maxLimit = Math.min(limit, 30) // Cap at 30 records for performance

    console.log('Fetching recent database records for pricing trends:', { limit: maxLimit })

    // Check if MongoDB connection is available
    if (!process.env.MONGODBCONNECTION) {
      console.error('MongoDB connection string not configured')
      return NextResponse.json(
        {
          error: 'Database not configured',
          details: 'MongoDB connection string is missing',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

    // Due to large documents in MongoDB causing memory issues, use local data directly
    console.log('Using local data due to MongoDB memory constraints')
    const { loadAllDatabaseRecords } = await import('@/utils/dataLoader')
    const records = await loadAllDatabaseRecords(maxLimit)

    console.log(`Found ${records.length} database records`)

    // If no records found, provide helpful message
    if (records.length === 0) {
      return NextResponse.json({
        success: true,
        records: [],
        count: 0,
        message: 'No database records found. Please upload Excel files with company data first.'
      })
    }

    return NextResponse.json({
      success: true,
      records,
      count: records.length
    })
  } catch (error) {
    console.error('Error fetching pricing trends data from MongoDB:', error)

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    const errorStack = error instanceof Error ? error.stack : undefined

    console.error('Full error details:', { errorMessage, errorStack })

    // Return empty records instead of error to prevent frontend crashes
    return NextResponse.json({
      success: true,
      records: [],
      count: 0,
      error: 'Failed to fetch pricing trends data',
      details: errorMessage,
      timestamp: new Date().toISOString()
    })
  }
}
