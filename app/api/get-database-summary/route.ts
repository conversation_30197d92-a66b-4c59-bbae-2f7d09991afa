import { NextRequest, NextResponse } from 'next/server'
import { getCollection } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get database records summary for pricing trends (lightweight)
 * GET /api/get-database-summary?limit=5 - Get recent database records summary
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = Math.min(parseInt(searchParams.get('limit') || '5'), 10) // Cap at 10

    console.log('Fetching database summary:', { limit })

    // Temporarily disable MongoDB queries due to large documents causing memory issues
    console.log('MongoDB queries temporarily disabled due to memory constraints')

    // Use local data instead
    const { loadAllDatabaseRecords } = await import('@/utils/dataLoader')
    const records = await loadAllDatabaseRecords(limit)

    return NextResponse.json({
      success: true,
      records,
      count: records.length,
      message: records.length === 0 ? 'No local data available. Please upload Excel files.' : undefined,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error fetching database summary:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json({
      success: true,
      records: [],
      count: 0,
      error: 'Failed to fetch database summary',
      details: errorMessage,
      timestamp: new Date().toISOString()
    })
  }
}
