import { NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Test MongoDB connection
 * GET /api/test-connection
 */
export async function GET() {
  try {
    console.log('Testing MongoDB connection...')
    
    // Check if connection string exists
    if (!process.env.MONGODBCONNECTION) {
      return NextResponse.json(
        {
          success: false,
          error: 'MongoDB connection string not configured',
          details: 'MONGODBCONNECTION environment variable is missing'
        },
        { status: 500 }
      )
    }

    // Test database connection
    const db = await getDatabase()
    
    // Try to ping the database
    await db.admin().ping()
    
    // Get collection stats
    const collection = db.collection('excel_uploads')
    const count = await collection.countDocuments()
    
    console.log('MongoDB connection successful, document count:', count)
    
    return NextResponse.json({
      success: true,
      message: 'MongoDB connection successful',
      database: 'exceldashboard',
      collection: 'excel_uploads',
      documentCount: count,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('MongoDB connection test failed:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    const errorStack = error instanceof Error ? error.stack : undefined
    
    return NextResponse.json(
      {
        success: false,
        error: 'MongoDB connection failed',
        details: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
