import { NextResponse } from 'next/server'

export const dynamic = 'force-dynamic';

/**
 * Get application status and database connectivity
 * GET /api/status
 */
export async function GET() {
  try {
    // Check if MongoDB connection string exists
    const hasMongoConnection = !!process.env.MONGODBCONNECTION
    
    // Try a simple connection test without loading large documents
    let mongoStatus = 'not_configured'
    let documentCount = 0
    
    if (hasMongoConnection) {
      try {
        const { getDatabase } = await import('@/lib/mongodb')
        const db = await getDatabase()
        await db.admin().ping()
        
        // Get document count without loading documents
        const collection = db.collection('excel_uploads')
        documentCount = await collection.countDocuments()
        
        mongoStatus = 'connected'
      } catch (error) {
        console.error('MongoDB connection test failed:', error)
        mongoStatus = 'connection_failed'
      }
    }
    
    // Check local data availability
    let localDataAvailable = false
    try {
      const fs = await import('fs')
      const path = await import('path')
      const dataDir = path.join(process.cwd(), 'data-sampling')
      localDataAvailable = fs.existsSync(dataDir)
    } catch (error) {
      console.error('Local data check failed:', error)
    }
    
    return NextResponse.json({
      success: true,
      status: 'operational',
      database: {
        mongodb: {
          configured: hasMongoConnection,
          status: mongoStatus,
          documentCount: documentCount
        },
        localData: {
          available: localDataAvailable
        }
      },
      recommendations: mongoStatus === 'connected' && documentCount > 0 ? 
        ['Database contains large documents that may cause memory issues', 'Consider using smaller Excel files or data pagination'] :
        ['Upload Excel files to get started', 'Ensure MongoDB connection is properly configured'],
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Status check failed:', error)
    
    return NextResponse.json({
      success: false,
      status: 'error',
      error: 'Status check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    })
  }
}
