import { NextResponse } from 'next/server'
import { loadAllDatabaseRecords } from '@/utils/dataLoader'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = searchParams.get('limit')
    const maxLimit = limit ? Math.min(parseInt(limit), 30) : 30
    
    const records = await loadAllDatabaseRecords(maxLimit)
    return NextResponse.json(records)
  } catch (error) {
    console.error('Error in database-records API:', error)
    return NextResponse.json(
      { error: 'Failed to load database records' },
      { status: 500 }
    )
  }
}