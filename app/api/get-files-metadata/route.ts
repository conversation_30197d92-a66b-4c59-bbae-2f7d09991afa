import { NextRequest, NextResponse } from 'next/server'
import { getCollection } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get uploaded files metadata only (no data content)
 * GET /api/get-files-metadata?limit=10&skip=0 - Get list of files metadata
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20) // Cap at 20
    const skip = parseInt(searchParams.get('skip') || '0')

    console.log('Fetching files metadata:', { limit, skip })

    // Temporarily disable MongoDB queries due to large documents causing memory issues
    console.log('MongoDB queries temporarily disabled due to memory constraints')
    return NextResponse.json({
      success: true,
      documents: [],
      count: 0,
      message: 'Database temporarily unavailable due to large documents. Please use file upload instead.',
      timestamp: new Date().toISOString()
    })

    // const collection = await getCollection('excel_uploads')
    
    // Only fetch metadata, no actual data content
    const documents = await collection
      .find({}, {
        projection: {
          filename: 1,
          uploadedAt: 1,
          uploadedBy: 1,
          metadata: 1,
          // Explicitly exclude the large data field
          data: 0
        }
      })
      .sort({ uploadedAt: -1 })
      .limit(limit)
      .skip(skip)
      .toArray()
    
    console.log(`Found ${documents.length} file metadata records`)
    
    // Transform documents to ensure proper serialization
    const result = documents.map(doc => ({
      _id: doc._id?.toString() || '',
      filename: doc.filename || 'Unknown',
      uploadedAt: doc.uploadedAt?.toISOString() || new Date().toISOString(),
      uploadedBy: doc.uploadedBy || 'anonymous',
      metadata: doc.metadata || {}
    }))

    return NextResponse.json({
      success: true,
      documents: result,
      count: result.length
    })
  } catch (error) {
    console.error('Error fetching files metadata:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json({
      success: false,
      documents: [],
      count: 0,
      error: 'Failed to fetch files metadata',
      details: errorMessage,
      timestamp: new Date().toISOString()
    })
  }
}
