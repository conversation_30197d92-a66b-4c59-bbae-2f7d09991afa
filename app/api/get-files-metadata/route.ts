import { NextRequest, NextResponse } from 'next/server'
import { getCollection } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get uploaded files metadata only (no data content)
 * GET /api/get-files-metadata?limit=10&skip=0 - Get list of files metadata
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20) // Cap at 20
    const skip = parseInt(searchParams.get('skip') || '0')

    console.log('Fetching files metadata:', { limit, skip })

    // Temporarily disable MongoDB queries due to large documents causing memory issues
    // The documents in the database are too large and cause Node.js to run out of memory
    console.log('MongoDB queries temporarily disabled due to memory constraints')

    return NextResponse.json({
      success: true,
      documents: [],
      count: 0,
      message: 'Database contains files but they are too large to display safely. The Excel files stored are causing memory issues when queried.',
      recommendation: 'The application will work normally for new file uploads. Existing files in database need optimization.',
      technicalDetails: 'MongoDB documents exceed Node.js heap memory limits during query operations.',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching files metadata:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json({
      success: false,
      documents: [],
      count: 0,
      error: 'Failed to fetch files metadata',
      details: errorMessage,
      timestamp: new Date().toISOString()
    })
  }
}
