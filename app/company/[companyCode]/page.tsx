"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON>outer } from "next/navigation"
import { useData } from "@/contexts/DataContext"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ArrowLeft, Building2, Brain, BarChart3, ChevronDown, ChevronRight } from "lucide-react"
import { formatNumber, formatPercentage } from "@/utils/dataHandler"
import { AIAnalysis } from "@/components/AIAnalysis"
import { DiagramGenerator } from "@/components/DiagramGenerator"
import { CompanySummary } from "@/components/CompanySummary"
import { CompanyPricingTrends } from "@/components/CompanyPricingTrends"
import { useState, useEffect } from "react"
import { loadAllDatabaseRecordsClient, type DatabaseRecord } from "@/utils/clientDataLoader"

const COMPANY_DETAIL_TABS = [
  { key: "insights", label: "Insights" },
  { key: "financial", label: "Financial" },
  { key: "performance", label: "Performance" },
  { key: "dividends", label: "Dividends" },
  { key: "pricing-trends", label: "Pricing Trends" },
  { key: "ai-analysis", label: "AI Analysis" },
  { key: "charts", label: "Charts" },
]

export default function CompanyDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { data } = useData()
  
  // State for collapsible sections in Insights tab
  const [openSections, setOpenSections] = useState({
    profile: true,
    business: false,
    ownership: false,
    leadership: false,
    kpis: true,
    trading: true,
    ratios: true,
    additional: false
  })
  
  // State for pricing trends data
  const [allDatabaseRecords, setAllDatabaseRecords] = useState<DatabaseRecord[]>([])
  const [loadingRecords, setLoadingRecords] = useState(false)
  
  // Load all database records for pricing trends
  useEffect(() => {
    const loadRecords = async () => {
      setLoadingRecords(true)
      try {
        const records = await loadAllDatabaseRecordsClient()
        setAllDatabaseRecords(records)
      } catch (error) {
        console.error('Failed to load database records:', error)
      } finally {
        setLoadingRecords(false)
      }
    }
    
    loadRecords()
  }, [])
  
  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section as keyof typeof prev]
    }))
  }
  const companyCode = decodeURIComponent(params.companyCode as string)

  if (!data) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-muted-foreground">No data available. Please upload an Excel file first.</p>
          <Button onClick={() => router.push("/")} className="mt-4">
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  // Debug logging to understand data structure
  console.log('=== COMPANY DETAIL PAGE DEBUG ===');
  console.log('Searching for company code:', companyCode);
  console.log('Available data sheets:', Object.keys(data));
  
  // Log sample data from each sheet to understand structure
  if (data.database && data.database.length > 0) {
    console.log('Database sheet sample (first row):', data.database[0]);
    console.log('Database sheet keys:', Object.keys(data.database[0]));
    console.log('Database symbols/codes:', data.database.slice(0, 5).map((row: any) => ({ 
      companyCode: row["Company code"], 
      symbol: row["Symbol"],
      companyName: row["Company Name"]
    })));
  }
  
  if (data.dailyPrice && data.dailyPrice.length > 0) {
    console.log('Daily Price sheet sample (first row):', data.dailyPrice[0]);
    console.log('Daily Price sheet keys:', Object.keys(data.dailyPrice[0]));
    console.log('Daily Price symbols:', data.dailyPrice.slice(0, 5).map((row: any) => ({ 
      symbol: row["Symbol"],
      companyName: row["Company Name"]
    })));
  }
  
  if (data.marketCap && data.marketCap.length > 0) {
    console.log('Market Cap sheet sample (first row):', data.marketCap[0]);
    console.log('Market Cap sheet keys:', Object.keys(data.marketCap[0]));
    console.log('Market Cap symbols:', data.marketCap.slice(0, 5).map((row: any) => ({ 
      symbol: row["Symbol"],
      companyName: row["Company Name"]
    })));
  }

  // Improved matching logic with better string handling
  const normalizeString = (str: any): string => {
    if (!str) return '';
    return str.toString().trim().toLowerCase();
  };
  
  const searchCode = normalizeString(companyCode);
  console.log('Normalized search code:', searchCode);

  // Find company data across all sheets with improved matching
  const companyData = {
    database: data.database?.find((row: any) => {
      const companyCodeMatch = normalizeString(row["Company code"]) === searchCode;
      const symbolMatch = normalizeString(row["Symbol"]) === searchCode;
      const found = companyCodeMatch || symbolMatch;
      if (found) {
        console.log('Found in database sheet:', { 
          companyCode: row["Company code"], 
          symbol: row["Symbol"],
          companyName: row["Company Name"]
        });
      }
      return found;
    }),
    dailyPrice: data.dailyPrice?.find((row: any) => {
      const symbolMatch = normalizeString(row["Symbol"]) === searchCode;
      if (symbolMatch) {
        console.log('Found in daily price sheet:', { 
          symbol: row["Symbol"],
          companyName: row["Company Name"]
        });
      }
      return symbolMatch;
    }),
    marketCap: data.marketCap?.find((row: any) => {
      const symbolMatch = normalizeString(row["Symbol"]) === searchCode;
      if (symbolMatch) {
        console.log('Found in market cap sheet:', { 
          symbol: row["Symbol"],
          companyName: row["Company Name"]
        });
      }
      return symbolMatch;
    }),
  };
  
  console.log('Final company data found:', {
    hasDatabase: !!companyData.database,
    hasDailyPrice: !!companyData.dailyPrice,
    hasMarketCap: !!companyData.marketCap
  });
  console.log('=== END DEBUG ===');

  if (!companyData.database && !companyData.dailyPrice && !companyData.marketCap) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Company not found: {companyCode}</p>
          <Button onClick={() => router.back()} className="mt-4">
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  // Get company name from available data sources
  const companyName = companyData.database?.["Company Name"] || companyData.dailyPrice?.["Company Name"] || companyData.marketCap?.["Company Name"] || companyCode;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/20 to-green-50/20 dark:via-blue-950/20 dark:to-green-950/20">
      <div className="container mx-auto py-8 animate-fade-in">
        <div className="mb-8">
          <Button variant="ghost" onClick={() => router.back()} className="mb-6 card-hover dark:text-foreground dark:hover:bg-muted/50">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>

          <div className="p-6 rounded-2xl bg-gradient-to-r from-card via-blue-50/30 to-green-50/30 dark:via-blue-950/30 dark:to-green-950/30 border border-border/50 shadow-colored dark:shadow-dark-elevated animate-slide-up">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-accent shadow-colored">
                <Building2 className="h-8 w-8 text-white" />
              </div>
              <div className="flex-1">
                <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary via-accent to-info bg-clip-text text-transparent dark:from-primary dark:via-accent dark:to-info">
                  {companyName}
                </h1>
                <div className="flex items-center gap-4 mt-2">
                  <p className="text-muted-foreground dark:text-muted-foreground text-lg">Symbol: <span className="font-mono font-semibold text-primary dark:text-primary">{companyCode}</span></p>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-success animate-pulse"></div>
                    <span className="text-sm text-success dark:text-success font-medium">Live Data</span>
                  </div>
                </div>
              </div>
              <div className="hidden sm:flex items-center gap-2">
                {companyData.database && (
                  <div className="px-3 py-1 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary text-sm font-medium border border-primary/20 dark:border-primary/30">
                    📊 Database
                  </div>
                )}
                {companyData.dailyPrice && (
                  <div className="px-3 py-1 rounded-full bg-success/10 dark:bg-success/20 text-success dark:text-success text-sm font-medium border border-success/20 dark:border-success/30">
                    📈 Pricing
                  </div>
                )}
                {companyData.marketCap && (
                  <div className="px-3 py-1 rounded-full bg-info/10 dark:bg-info/20 text-info dark:text-info text-sm font-medium border border-info/20 dark:border-info/30">
                    💰 Market Cap
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="insights" className="w-full animate-scale-in">
          <TabsList className="w-full bg-gradient-to-r from-muted via-blue-50/50 to-green-50/50 dark:via-blue-950/50 dark:to-green-950/50 p-1 rounded-xl shadow-colored dark:shadow-dark-elevated">
            {COMPANY_DETAIL_TABS.map((tab, index) => (
              <TabsTrigger
                key={tab.key}
                value={tab.key}
                className="text-xs sm:text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-accent data-[state=active]:text-white data-[state=active]:shadow-colored hover:bg-white/50 dark:hover:bg-white/10 dark:text-foreground"
              >
                <span className="mr-1">
                  {tab.key === "insights" && "💡"}
                  {tab.key === "financial" && "💰"}
                  {tab.key === "performance" && "📊"}
                  {tab.key === "dividends" && "💎"}
                  {tab.key === "pricing-trends" && "📈"}
                  {tab.key === "ai-analysis" && "🤖"}
                  {tab.key === "charts" && "📋"}
                </span>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

        <TabsContent value="insights" className="mt-8 animate-fade-in">
          <div className="space-y-6">
            {/* Company Profile Section */}
            <Collapsible open={openSections.profile} onOpenChange={() => toggleSection('profile')}>
              <Card className="card-hover border-0 shadow-colored dark:shadow-dark-elevated bg-gradient-to-br from-card via-blue-50/10 to-green-50/10 dark:via-blue-950/20 dark:to-green-950/20">
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5 dark:hover:from-primary/10 dark:hover:to-accent/10 transition-all duration-300 rounded-t-xl">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/30 dark:to-accent/30">
                          <Building2 className="h-5 w-5 text-primary dark:text-primary" />
                        </div>
                        <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent dark:from-primary dark:to-accent">
                          Company Profile
                        </span>
                      </div>
                      {openSections.profile ? <ChevronDown className="h-4 w-4 text-primary dark:text-primary" /> : <ChevronRight className="h-4 w-4 text-muted-foreground dark:text-muted-foreground" />}
                    </CardTitle>
                    <CompanySummary companyData={companyData} companyName={companyName} />
                  </CardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                          <p className="text-base sm:text-lg font-semibold">{companyName}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Symbol/Code</label>
                          <p className="text-base sm:text-lg font-mono">{companyCode}</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Sector</label>
                          <p className="text-base sm:text-lg">{companyData.database?.["Sector"] || "N/A"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Market Cap Category</label>
                          <p className="text-base sm:text-lg">{companyData.database?.["Market Cap Category"] || "N/A"}</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Data Sources</label>
                          <div className="flex flex-wrap gap-2">
                            {companyData.database && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Database</span>
                            )}
                            {companyData.dailyPrice && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                Daily Price
                              </span>
                            )}
                            {companyData.marketCap && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                                Market Cap
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>

            {/* Key Performance Indicators Section */}
            <Collapsible open={openSections.kpis} onOpenChange={() => toggleSection('kpis')}>
              <Card>
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-primary" />
                        Key Performance Indicators
                      </div>
                      {openSections.kpis ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </CardTitle>
                    <CardDescription>Current market performance and key financial metrics</CardDescription>
                  </CardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {/* Current Price - Always show if any price data exists */}
                  {(() => {
                    // Try to find price from multiple possible field names
                    const priceValue = 
                      companyData.dailyPrice?.["**Last Trade (Rs.)"] || // Correct key for dailyPrice
                      companyData.database?.["Current price"] ||       // Aligned with Data base.md structure
                      companyData.dailyPrice?.['Last Trade'] ||
                      companyData.dailyPrice?.['Price'] ||
                      companyData.database?.['Current Price (Rs.)'] ||
                      companyData.database?.['Price (Rs.)'] ||
                      companyData.database?.['Price'] ||
                      companyData.database?.['Current Price'] ||
                      companyData.database?.['Last Price'] ||
                      companyData.database?.['Market Price'] ||
                      "N/A"; // Fallback value if no price is found
                    
                    return (
                      <div className="text-left p-4 border rounded-lg">
                        <div className="text-xl sm:text-2xl font-bold text-primary">
                          Rs. {formatNumber(priceValue)}
                        </div>
                        <div className="text-sm text-muted-foreground mb-2">Current Price</div>
                        {companyData.dailyPrice?.['Change (%)'] && (
                          <div
                            className={`text-sm font-medium ${
                              Number.parseFloat(companyData.dailyPrice['Change (%)']) >= 0
                                ? 'text-green-600'
                                : 'text-red-600'
                            }`}
                          >
                            {companyData.dailyPrice['Change (%)']}% ({companyData.dailyPrice['Change (Rs)']})  
                          </div>
                        )}
                      </div>
                    );
                  })()}

                  {companyData.marketCap && (
                    <div className="text-left p-4 border rounded-lg">
                      <div className="text-xl sm:text-2xl font-bold text-primary">
                        {formatNumber(companyData.marketCap["Market Capitalization"])}
                      </div>
                      <div className="text-sm text-muted-foreground mb-2">Market Cap</div>
                      <div className="text-sm text-muted-foreground">
                        {formatPercentage(companyData.marketCap["Market Cap. (as a % of Total Market Cap.)"])} of total
                        market
                      </div>
                    </div>
                  )}

                  {companyData.database?.["ROE"] && (
                    <div className="text-left p-4 border rounded-lg">
                      <div className="text-xl sm:text-2xl font-bold text-primary">
                        {formatPercentage(companyData.database["ROE"])}
                      </div>
                      <div className="text-sm text-muted-foreground mb-2">Return on Equity</div>
                      <div className="text-sm text-muted-foreground">ROE Performance</div>
                    </div>
                  )}

                  {companyData.database?.["BETA VALUES AGAINST ASPI"] && (
                    <div className="text-left p-4 border rounded-lg">
                      <div className="text-xl sm:text-2xl font-bold text-primary">
                        {formatNumber(companyData.database["BETA VALUES AGAINST ASPI"])}
                      </div>
                      <div className="text-sm text-muted-foreground mb-2">Beta</div>
                      <div className="text-sm text-muted-foreground">Market Risk Measure</div>
                    </div>
                  )}
                </div>
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>

            {/* Trading Information Section */}
            {companyData.dailyPrice && (
              <Collapsible open={openSections.trading} onOpenChange={() => toggleSection('trading')}>
                <Card>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <BarChart3 className="h-5 w-5 text-primary" />
                          Trading Information
                        </div>
                        {openSections.trading ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </CardTitle>
                      <CardDescription>Daily trading data and volume information</CardDescription>
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                        Price Range
                      </h4>
                      <div className="space-y-3">
                        <div className="flex justify-start">
                          <span className="w-1/2">Previous Close:</span>
                          <span className="font-medium text-left w-1/2">
                            Rs. {formatNumber(companyData.dailyPrice["Previous Close (Rs.)"])}
                          </span>
                        </div>
                        <div className="flex justify-start">
                          <span className="w-1/2">Open:</span>
                          <span className="font-medium text-left w-1/2">Rs. {formatNumber(companyData.dailyPrice["Open (Rs.)"])}</span>
                        </div>
                        <div className="flex justify-start">
                          <span className="w-1/2">High:</span>
                          <span className="font-medium text-green-600 text-left w-1/2">
                            Rs. {formatNumber(companyData.dailyPrice["High (Rs.)"])}
                          </span>
                        </div>
                        <div className="flex justify-start">
                          <span className="w-1/2">Low:</span>
                          <span className="font-medium text-red-600 text-left w-1/2">
                            Rs. {formatNumber(companyData.dailyPrice["Low (Rs.)"])}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Volume</h4>
                      <div className="space-y-3">
                        <div className="flex justify-start">
                          <span className="w-1/2">Share Volume:</span>
                          <span className="font-medium text-left w-1/2">{formatNumber(companyData.dailyPrice["Share Volume"])}</span>
                        </div>
                        <div className="flex justify-start">
                          <span className="w-1/2">Trade Volume:</span>
                          <span className="font-medium text-left w-1/2">{formatNumber(companyData.dailyPrice["Trade Volume"])}</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                        Market Position
                      </h4>
                      <div className="space-y-3">
                        {companyData.marketCap && (
                          <>
                            <div className="flex justify-start">
                              <span className="w-1/2">Issued Quantity:</span>
                              <span className="font-medium text-left w-1/2">
                                {formatNumber(companyData.marketCap["Issued Quantity"])}
                              </span>
                            </div>
                            <div className="flex justify-start">
                              <span className="w-1/2">Market Share:</span>
                              <span className="font-medium text-left w-1/2">
                                {formatPercentage(companyData.marketCap["Market Cap. (as a % of Total Market Cap.)"])}
                              </span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            )}

            {/* Financial Ratios & Metrics Section */}
            {companyData.database && (
              <Collapsible open={openSections.ratios} onOpenChange={() => toggleSection('ratios')}>
                <Card>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Brain className="h-5 w-5 text-primary" />
                          Financial Ratios & Key Metrics
                        </div>
                        {openSections.ratios ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </CardTitle>
                      <CardDescription>Comprehensive financial analysis and performance indicators</CardDescription>
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {/* Profitability Ratios */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                        Profitability
                      </h4>
                      {Object.entries(companyData.database)
                        .filter(
                          ([key]) =>
                            key.toLowerCase().includes("roe") ||
                            key.toLowerCase().includes("profit") ||
                            key.toLowerCase().includes("margin"),
                        )
                        .filter(([, value]) => value !== null && value !== undefined && value !== "")
                        .map(([key, value]) => (
                          <div key={key} className="flex justify-start py-2 border-b">
                            <span className="w-1/2">{key}:</span>
                            <span className="font-medium text-left w-1/2">{formatNumber(value)}</span>
                          </div>
                        ))}
                    </div>

                    {/* Valuation Ratios */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Valuation</h4>
                      <div className="space-y-3">
                        {Object.entries(companyData.database)
                          .filter(
                            ([key]) =>
                              key.toLowerCase().includes("pbv") ||
                              key.toLowerCase().includes("pe") ||
                              key.toLowerCase().includes("eps") ||
                              key.toLowerCase().includes("beta"),
                          )
                          .filter(([, value]) => value !== null && value !== undefined && value !== "")
                          .map(([key, value]) => (
                            <div key={key} className="flex justify-start py-2 border-b">
                              <span className="w-1/2">{key}:</span>
                              <span className="font-medium text-left w-1/2">{formatNumber(value)}</span>
                            </div>
                          ))}
                      </div>
                    </div>

                    {/* Other Financial Metrics */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                        Other Metrics
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(companyData.database)
                          .filter(
                            ([key]) =>
                              key.toLowerCase().includes("ratio") ||
                              key.toLowerCase().includes("debt") ||
                              key.toLowerCase().includes("equity") ||
                              key.toLowerCase().includes("dividend"),
                          )
                          .filter(([, value]) => value !== null && value !== undefined && value !== "")
                          .slice(0, 8) // Limit to prevent overcrowding
                          .map(([key, value]) => (
                            <div key={key} className="flex justify-start py-2 border-b">
                              <span className="w-1/2">{key}:</span>
                              <span className="font-medium text-left w-1/2">{formatNumber(value)}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            )}

            {/* Additional Company Information Section */}
            {companyData.database && (
              <Collapsible open={openSections.additional} onOpenChange={() => toggleSection('additional')}>
                <Card>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Building2 className="h-5 w-5 text-primary" />
                          Additional Company Information
                        </div>
                        {openSections.additional ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </CardTitle>
                      <CardDescription>Complete database information for comprehensive analysis</CardDescription>
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent>
                  <div className="max-h-96 overflow-y-auto">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {Object.entries(companyData.database)
                        .filter(
                          ([key, value]) =>
                            value !== null &&
                            value !== undefined &&
                            value !== "" &&
                            !["Company Name", "Company code", "Symbol", "Sector", "Market Cap Category"].includes(key),
                        )
                        .map(([key, value]) => (
                          <div key={key} className="flex justify-start py-2 border-b border-border/30">
                            <span className="font-medium text-sm w-1/2">{key}:</span>
                            <span className="text-sm text-left w-1/2" title={String(value)}>
                              {formatNumber(value)}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            )}
          </div>
        </TabsContent>

        <TabsContent value="financial" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Financial Metrics</CardTitle>
              <CardDescription>Key financial indicators and ratios</CardDescription>
            </CardHeader>
            <CardContent>
              {companyData.database ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Object.entries(companyData.database)
                    .filter(
                      ([key]) =>
                        key.toLowerCase().includes("ratio") ||
                        key.toLowerCase().includes("roe") ||
                        key.toLowerCase().includes("eps") ||
                        key.toLowerCase().includes("pbv") ||
                        key.toLowerCase().includes("debt") ||
                        key.toLowerCase().includes("equity"),
                    )
                    .map(([key, value]) => (
                      <div key={key} className="flex justify-start py-2 border-b">
                        <span className="font-medium w-1/2">{key}:</span>
                        <span className="text-left w-1/2">{formatNumber(value)}</span>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No financial metrics available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {companyData.dailyPrice && (
              <Card>
                <CardHeader>
                  <CardTitle>Daily Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-start">
                      <span className="w-1/2">Open:</span>
                      <span className="text-left w-1/2">Rs. {formatNumber(companyData.dailyPrice["Open (Rs.)"])}</span>
                    </div>
                    <div className="flex justify-start">
                      <span className="w-1/2">High:</span>
                      <span className="text-left w-1/2">Rs. {formatNumber(companyData.dailyPrice["High (Rs.)"])}</span>
                    </div>
                    <div className="flex justify-start">
                      <span className="w-1/2">Low:</span>
                      <span className="text-left w-1/2">Rs. {formatNumber(companyData.dailyPrice["Low (Rs.)"])}</span>
                    </div>
                    <div className="flex justify-start">
                      <span className="w-1/2">Volume:</span>
                      <span className="text-left w-1/2">{formatNumber(companyData.dailyPrice["Share Volume"])}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {companyData.database && (
              <Card>
                <CardHeader>
                  <CardTitle>Key Ratios</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {companyData.database["BETA VALUES AGAINST ASPI"] && (
                  <div className="flex justify-between">
                    <span>Beta:</span>
                    <span>{formatNumber(companyData.database["BETA VALUES AGAINST ASPI"])}</span>
                  </div>
                )}
                    {companyData.database["ROE"] && (
                      <div className="flex justify-between">
                        <span>ROE:</span>
                        <span>{formatPercentage(companyData.database["ROE"])}</span>
                      </div>
                    )}
                    {companyData.database["PBV"] && (
                      <div className="flex justify-between">
                        <span>PBV:</span>
                        <span>{formatNumber(companyData.database["PBV"])}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="dividends" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Dividends & Returns</CardTitle>
              <CardDescription>Dividend history and yield information</CardDescription>
            </CardHeader>
            <CardContent>
              {companyData.database ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Object.entries(companyData.database)
                    .filter(([key]) => key.toLowerCase().includes("dividend") || key.toLowerCase().includes("yield"))
                    .map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b">
                        <span className="font-medium">{key}:</span>
                        <span>{formatNumber(value)}</span>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No dividend information available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-analysis" className="mt-6">
          <AIAnalysis selectedCompany={companyCode} companyData={companyData} />
        </TabsContent>

        <TabsContent value="pricing-trends" className="mt-6">
          {loadingRecords ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  Loading pricing trend data...
                </div>
              </CardContent>
            </Card>
          ) : (
            <CompanyPricingTrends 
              companyCode={params.companyCode} 
              allDatabaseRecords={allDatabaseRecords} 
            />
          )}
        </TabsContent>

        <TabsContent value="charts" className="mt-6">
          <DiagramGenerator selectedCompany={companyCode} companyData={companyData} />
        </TabsContent>
      </Tabs>
      </div>
    </div>
  )
}
