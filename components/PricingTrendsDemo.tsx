'use client'

import React, { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts"
import { TrendingUp, TrendingDown, BarChart3, Search } from "lucide-react"
import { formatNumber } from "@/utils/dataHandler"

// Chart configuration for consistent styling
const chartConfig = {
  currentPrice: {
    label: "Current Price",
    color: "hsl(var(--chart-1))",
  },
  priceChange: {
    label: "Price Change %",
    color: "hsl(var(--chart-2))",
  },
}

// Color palette for multiple companies
const COMPANY_COLORS = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
  "hsl(var(--chart-5))",
  "#8B5CF6", // Purple
  "#F59E0B", // Amber
  "#EF4444", // Red
]

// Sample data for demonstration
const sampleData = [
  {
    date: "2025-01-01",
    companies: {
      "SAMP.N0000": { name: "SAMPATH BANK PLC", price: 85.50, pe: 12.5, eps: 6.84 },
      "COMB.N0000": { name: "COMMERCIAL BANK PLC", price: 92.25, pe: 11.8, eps: 7.82 },
      "HNB.N0000": { name: "HATTON NATIONAL BANK PLC", price: 178.00, pe: 9.2, eps: 19.35 },
      "DFCC.N0000": { name: "DFCC BANK PLC", price: 45.75, pe: 15.3, eps: 2.99 },
    }
  },
  {
    date: "2025-01-05",
    companies: {
      "SAMP.N0000": { name: "SAMPATH BANK PLC", price: 87.25, pe: 12.3, eps: 7.09 },
      "COMB.N0000": { name: "COMMERCIAL BANK PLC", price: 94.50, pe: 11.5, eps: 8.22 },
      "HNB.N0000": { name: "HATTON NATIONAL BANK PLC", price: 182.50, pe: 9.0, eps: 20.28 },
      "DFCC.N0000": { name: "DFCC BANK PLC", price: 47.25, pe: 15.0, eps: 3.15 },
    }
  },
  {
    date: "2025-01-10",
    companies: {
      "SAMP.N0000": { name: "SAMPATH BANK PLC", price: 89.75, pe: 12.1, eps: 7.42 },
      "COMB.N0000": { name: "COMMERCIAL BANK PLC", price: 96.00, pe: 11.3, eps: 8.50 },
      "HNB.N0000": { name: "HATTON NATIONAL BANK PLC", price: 185.25, pe: 8.9, eps: 20.82 },
      "DFCC.N0000": { name: "DFCC BANK PLC", price: 48.50, pe: 14.8, eps: 3.28 },
    }
  }
]

export function PricingTrendsDemo() {
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>(["SAMP.N0000", "COMB.N0000"])
  const [searchTerm, setSearchTerm] = useState("")
  const [activeChart, setActiveChart] = useState("price-trends")

  // Extract available companies from sample data
  const availableCompanies = useMemo(() => {
    const companies = sampleData[0]?.companies || {}
    return Object.entries(companies).map(([code, data]) => ({
      code,
      name: data.name,
      currentPrice: data.price,
      sector: "Banking"
    }))
  }, [])

  // Filter companies based on search term
  const filteredCompanies = useMemo(() => {
    return availableCompanies.filter(company => 
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.code.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [availableCompanies, searchTerm])

  // Prepare chart data
  const chartData = useMemo(() => {
    return sampleData.map((record, index) => {
      const dataPoint: any = { date: record.date }
      
      selectedCompanies.forEach(companyCode => {
        const company = record.companies[companyCode]
        if (company) {
          dataPoint[`${companyCode}_price`] = company.price
          
          // Calculate price change from previous record
          if (index > 0) {
            const prevPrice = sampleData[index - 1].companies[companyCode]?.price
            if (prevPrice) {
              const change = ((company.price - prevPrice) / prevPrice) * 100
              dataPoint[`${companyCode}_change`] = change
            }
          } else {
            dataPoint[`${companyCode}_change`] = 0
          }
        }
      })
      
      return dataPoint
    })
  }, [selectedCompanies])

  // Handle company selection
  const toggleCompanySelection = (companyCode: string) => {
    setSelectedCompanies(prev => {
      if (prev.includes(companyCode)) {
        return prev.filter(code => code !== companyCode)
      } else if (prev.length < 4) { // Limit to 4 companies for demo
        return [...prev, companyCode]
      }
      return prev
    })
  }

  const clearSelections = () => {
    setSelectedCompanies([])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Enhanced Pricing Trends - Demo
          </CardTitle>
          <CardDescription>
            Interactive demo showing price changes across sample banking companies. 
            This demonstrates how the feature works with real MongoDB data.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Selection Controls */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search Companies</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Selected Companies</Label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedCompanies.length}/4 selected
                </Badge>
                {selectedCompanies.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearSelections}>
                    Clear All
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Company Selection Grid */}
          <div className="space-y-2">
            <Label>Available Companies ({filteredCompanies.length})</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {filteredCompanies.map(company => (
                <div
                  key={company.code}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedCompanies.includes(company.code)
                      ? "bg-primary/10 border-primary"
                      : "hover:bg-muted"
                  }`}
                  onClick={() => toggleCompanySelection(company.code)}
                >
                  <div className="font-medium text-sm">{company.name}</div>
                  <div className="text-xs text-muted-foreground">{company.code}</div>
                  <div className="text-xs text-muted-foreground">
                    Price: {formatNumber(company.currentPrice)} | Sector: {company.sector}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      {selectedCompanies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Historical Pricing Analysis - Demo</CardTitle>
            <CardDescription>
              Visual comparison of selected companies across sample time periods
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeChart} onValueChange={setActiveChart}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="price-trends">Price Trends</TabsTrigger>
                <TabsTrigger value="price-changes">Price Changes</TabsTrigger>
              </TabsList>

              {/* Price Trends Line Chart */}
              <TabsContent value="price-trends" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Price Trends Over Time</CardTitle>
                    <CardDescription>
                      Historical price movements for selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis 
                            dataKey="date" 
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip 
                            content={<ChartTooltipContent />}
                            formatter={(value, name) => [
                              formatNumber(value as number),
                              name.replace('_price', '').replace('_', ' ')
                            ]}
                          />
                          <ChartLegend content={<ChartLegendContent />} />
                          {selectedCompanies.map((companyCode, index) => {
                            const company = availableCompanies.find(c => c.code === companyCode)
                            return (
                              <Line
                                key={companyCode}
                                type="monotone"
                                dataKey={`${companyCode}_price`}
                                stroke={COMPANY_COLORS[index % COMPANY_COLORS.length]}
                                strokeWidth={2}
                                dot={{ r: 4 }}
                                name={company?.name || companyCode}
                              />
                            )
                          })}
                        </LineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Price Changes Bar Chart */}
              <TabsContent value="price-changes" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Price Change Percentage</CardTitle>
                    <CardDescription>
                      Percentage change in prices between time periods
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis 
                            dataKey="date" 
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip 
                            content={<ChartTooltipContent />}
                            formatter={(value, name) => [
                              `${formatNumber(value as number)}%`,
                              name.replace('_change', '').replace('_', ' ')
                            ]}
                          />
                          <ChartLegend content={<ChartLegendContent />} />
                          {selectedCompanies.map((companyCode, index) => {
                            const company = availableCompanies.find(c => c.code === companyCode)
                            return (
                              <Bar
                                key={companyCode}
                                dataKey={`${companyCode}_change`}
                                fill={COMPANY_COLORS[index % COMPANY_COLORS.length]}
                                name={company?.name || companyCode}
                                radius={[2, 2, 0, 0]}
                              />
                            )
                          })}
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Summary Statistics */}
      {selectedCompanies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Demo Summary Statistics</CardTitle>
            <CardDescription>
              Key statistics for selected companies based on sample data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {selectedCompanies.length}
                </div>
                <div className="text-sm text-muted-foreground">Companies Selected</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {sampleData.length}
                </div>
                <div className="text-sm text-muted-foreground">Time Periods</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  <TrendingUp className="h-5 w-5 inline mr-1" />
                  Demo
                </div>
                <div className="text-sm text-muted-foreground">Mode Active</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
