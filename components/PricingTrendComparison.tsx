'use client'

import React, { useState, useMemo } from "react"
import { useData } from "@/contexts/DataContext"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"
import { TrendingUp, TrendingDown, BarChart3, Search, Filter } from "lucide-react"
import { formatNumber, formatPercentage } from "@/utils/dataHandler"

// Chart configuration for consistent styling
const chartConfig = {
  currentPrice: {
    label: "Current Price",
    color: "hsl(var(--chart-1))",
  },
  pe: {
    label: "P/E Ratio",
    color: "hsl(var(--chart-2))",
  },
  pbv: {
    label: "P/B Ratio",
    color: "hsl(var(--chart-3))",
  },
  eps: {
    label: "EPS",
    color: "hsl(var(--chart-4))",
  },
  dividendYield: {
    label: "Dividend Yield",
    color: "hsl(var(--chart-5))",
  },
}

// Color palette for pie charts
const COLORS = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
  "hsl(var(--chart-5))",
]

export function PricingTrendComparison() {
  const { data } = useData()
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [sectorFilter, setSectorFilter] = useState("all")
  const [activeChart, setActiveChart] = useState("price-comparison")

  // Extract company data from database
  const companies = useMemo(() => {
    if (!data?.database) return []
    
    return data.database
      .filter((company: any) => 
        company?.Company && 
        company?."Company code" && 
        company?."Current price" !== null &&
        company?."Current price" !== undefined
      )
      .map((company: any) => ({
        name: company.Company,
        code: company["Company code"],
        currentPrice: parseFloat(company["Current price"]) || 0,
        pe: parseFloat(company.PE) || 0,
        pbv: parseFloat(company.PBV) || 0,
        eps: parseFloat(company["Earning per share (Annual)"]) || 0,
        dividendYield: parseFloat(company["Dividend yield"]) || 0,
        marketCap: parseFloat(company["Market cap/ Total Market cap"]) || 0,
        sector: company.Sector || "Unknown",
        netAssetPerShare: parseFloat(company["Net Asset per share"]) || 0,
        roe: parseFloat(company.ROE) || 0,
        roa: parseFloat(company.ROA) || 0,
        beta: parseFloat(company["BETA VALUES AGAINST ASPI"]) || 0,
        revenueGrowth2024: parseFloat(company["Revenue Growth 2024"]) || 0,
        revenueGrowth2023: parseFloat(company["Revenue Growth 2023"]) || 0,
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }, [data])

  // Get unique sectors for filtering
  const sectors = useMemo(() => {
    const uniqueSectors = [...new Set(companies.map(c => c.sector))]
    return uniqueSectors.filter(sector => sector && sector !== "Unknown").sort()
  }, [companies])

  // Filter companies based on search and sector
  const filteredCompanies = useMemo(() => {
    return companies.filter(company => {
      const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           company.code.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesSector = sectorFilter === "all" || company.sector === sectorFilter
      return matchesSearch && matchesSector
    })
  }, [companies, searchTerm, sectorFilter])

  // Get selected companies data
  const selectedCompaniesData = useMemo(() => {
    return companies.filter(company => selectedCompanies.includes(company.code))
  }, [companies, selectedCompanies])

  // Handle company selection
  const toggleCompanySelection = (companyCode: string) => {
    setSelectedCompanies(prev => {
      if (prev.includes(companyCode)) {
        return prev.filter(code => code !== companyCode)
      } else if (prev.length < 10) { // Limit to 10 companies for readability
        return [...prev, companyCode]
      }
      return prev
    })
  }

  // Clear all selections
  const clearSelections = () => {
    setSelectedCompanies([])
  }

  // Chart data preparation functions
  const getPriceComparisonData = () => {
    return selectedCompaniesData.map(company => ({
      name: company.name.length > 15 ? company.name.substring(0, 15) + '...' : company.name,
      currentPrice: company.currentPrice,
      code: company.code,
    }))
  }

  const getValuationMetricsData = () => {
    return selectedCompaniesData.map(company => ({
      name: company.name.length > 15 ? company.name.substring(0, 15) + '...' : company.name,
      pe: company.pe,
      pbv: company.pbv,
      code: company.code,
    }))
  }

  const getPerformanceMetricsData = () => {
    return selectedCompaniesData.map(company => ({
      name: company.name.length > 15 ? company.name.substring(0, 15) + '...' : company.name,
      eps: company.eps,
      roe: company.roe,
      roa: company.roa,
      code: company.code,
    }))
  }

  const getDividendAnalysisData = () => {
    return selectedCompaniesData
      .filter(company => company.dividendYield > 0)
      .map(company => ({
        name: company.name.length > 15 ? company.name.substring(0, 15) + '...' : company.name,
        dividendYield: company.dividendYield * 100, // Convert to percentage
        code: company.code,
      }))
  }

  const getSectorDistributionData = () => {
    const sectorCounts = selectedCompaniesData.reduce((acc, company) => {
      acc[company.sector] = (acc[company.sector] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(sectorCounts).map(([sector, count]) => ({
      name: sector,
      value: count,
    }))
  }

  const getRiskReturnData = () => {
    return selectedCompaniesData
      .filter(company => company.beta > 0 && company.roe > 0)
      .map(company => ({
        name: company.name.length > 15 ? company.name.substring(0, 15) + '...' : company.name,
        risk: company.beta,
        return: company.roe,
        code: company.code,
        size: company.marketCap * 10, // Scale for bubble size
      }))
  }

  if (!data?.database) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pricing Trend Comparison</CardTitle>
          <CardDescription>
            No database information available. Please upload an Excel file with company data.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Pricing Trend Comparison
          </CardTitle>
          <CardDescription>
            Compare pricing trends and financial metrics across multiple companies. Select up to 10 companies to analyze.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Filter Controls */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search Companies</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="sector">Filter by Sector</Label>
              <Select value={sectorFilter} onValueChange={setSectorFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All sectors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sectors</SelectItem>
                  {sectors.map(sector => (
                    <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Selected Companies</Label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedCompanies.length}/10 selected
                </Badge>
                {selectedCompanies.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearSelections}>
                    Clear All
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Company Selection Grid */}
          <div className="space-y-2">
            <Label>Available Companies ({filteredCompanies.length})</Label>
            <div className="max-h-60 overflow-y-auto border rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {filteredCompanies.map(company => (
                  <div
                    key={company.code}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedCompanies.includes(company.code)
                        ? "bg-primary/10 border-primary"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => toggleCompanySelection(company.code)}
                  >
                    <div className="font-medium text-sm">{company.name}</div>
                    <div className="text-xs text-muted-foreground">{company.code}</div>
                    <div className="text-xs text-muted-foreground">
                      Price: {formatNumber(company.currentPrice)} | Sector: {company.sector}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      {selectedCompanies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Comparative Analysis Charts</CardTitle>
            <CardDescription>
              Visual comparison of selected companies across different financial metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeChart} onValueChange={setActiveChart}>
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="price-comparison">Price</TabsTrigger>
                <TabsTrigger value="valuation">Valuation</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="dividends">Dividends</TabsTrigger>
                <TabsTrigger value="sectors">Sectors</TabsTrigger>
                <TabsTrigger value="risk-return">Risk/Return</TabsTrigger>
              </TabsList>

              {/* Price Comparison Chart */}
              <TabsContent value="price-comparison" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Current Price Comparison</CardTitle>
                    <CardDescription>
                      Compare current stock prices across selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <BarChart data={getPriceComparisonData()}>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                        <XAxis 
                          dataKey="name" 
                          angle={-45}
                          textAnchor="end"
                          height={100}
                          fontSize={12}
                          tick={{ fill: "hsl(var(--foreground))" }}
                          stroke="hsl(var(--muted-foreground))"
                        />
                        <YAxis tick={{ fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                        <ChartTooltip 
                          content={<ChartTooltipContent />}
                          formatter={(value, name, props) => [
                            formatNumber(value as number),
                            "Current Price"
                          ]}
                        />
                        <Bar 
                          dataKey="currentPrice" 
                          fill="var(--color-currentPrice)"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Valuation Metrics Chart */}
              <TabsContent value="valuation" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Valuation Metrics Comparison</CardTitle>
                    <CardDescription>
                      Compare P/E and P/B ratios across selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <BarChart data={getValuationMetricsData()}>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                        <XAxis 
                          dataKey="name" 
                          angle={-45}
                          textAnchor="end"
                          height={100}
                          fontSize={12}
                          tick={{ fill: "hsl(var(--foreground))" }}
                          stroke="hsl(var(--muted-foreground))"
                        />
                        <YAxis tick={{ fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <ChartLegend content={<ChartLegendContent />} />
                        <Bar dataKey="pe" fill="var(--color-pe)" name="P/E Ratio" />
                        <Bar dataKey="pbv" fill="var(--color-pbv)" name="P/B Ratio" />
                      </BarChart>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Performance Metrics Chart */}
              <TabsContent value="performance" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics Comparison</CardTitle>
                    <CardDescription>
                      Compare EPS, ROE, and ROA across selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <BarChart data={getPerformanceMetricsData()}>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                        <XAxis 
                          dataKey="name" 
                          angle={-45}
                          textAnchor="end"
                          height={100}
                          fontSize={12}
                          tick={{ fill: "hsl(var(--foreground))" }}
                          stroke="hsl(var(--muted-foreground))"
                        />
                        <YAxis tick={{ fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <ChartLegend content={<ChartLegendContent />} />
                        <Bar dataKey="eps" fill="var(--color-eps)" name="EPS" />
                        <Bar dataKey="roe" fill="var(--color-pe)" name="ROE (%)" />
                        <Bar dataKey="roa" fill="var(--color-pbv)" name="ROA (%)" />
                      </BarChart>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Dividend Analysis Chart */}
              <TabsContent value="dividends" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Dividend Yield Comparison</CardTitle>
                    <CardDescription>
                      Compare dividend yields across selected companies (companies with dividends only)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getDividendAnalysisData().length > 0 ? (
                      <ChartContainer config={chartConfig} className="h-[400px]">
                        <BarChart data={getDividendAnalysisData()}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis 
                            dataKey="name" 
                            angle={-45}
                            textAnchor="end"
                            height={100}
                            fontSize={12}
                            tick={{ fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                          />
                          <YAxis tick={{ fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip 
                            content={<ChartTooltipContent />}
                            formatter={(value) => [`${formatNumber(value as number)}%`, "Dividend Yield"]}
                          />
                          <Bar 
                            dataKey="dividendYield" 
                            fill="var(--color-dividendYield)"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ChartContainer>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No dividend data available for selected companies
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Sector Distribution Chart */}
              <TabsContent value="sectors" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Sector Distribution</CardTitle>
                    <CardDescription>
                      Distribution of selected companies across different sectors
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <PieChart>
                        <Pie
                          data={getSectorDistributionData()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={120}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {getSectorDistributionData().map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <ChartTooltip content={<ChartTooltipContent />} />
                      </PieChart>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Risk-Return Analysis Chart */}
              <TabsContent value="risk-return" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Risk vs Return Analysis</CardTitle>
                    <CardDescription>
                      Beta (risk) vs ROE (return) scatter plot for selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getRiskReturnData().length > 0 ? (
                      <ChartContainer config={chartConfig} className="h-[400px]">
                        <ScatterChart data={getRiskReturnData()}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis 
                            type="number" 
                            dataKey="risk" 
                            name="Beta (Risk)"
                            domain={['dataMin - 0.1', 'dataMax + 0.1']}
                            tick={{ fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                          />
                          <YAxis 
                            type="number" 
                            dataKey="return" 
                            name="ROE (Return %)"
                            domain={['dataMin - 1', 'dataMax + 1']}
                            tick={{ fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                          />
                          <ChartTooltip 
                            content={<ChartTooltipContent />}
                            formatter={(value, name) => {
                              if (name === "risk") return [formatNumber(value as number), "Beta (Risk)"]
                              if (name === "return") return [`${formatNumber(value as number)}%`, "ROE (Return)"]
                              return [value, name]
                            }}
                          />
                          <Scatter 
                            dataKey="return" 
                            fill="var(--color-pe)"
                          />
                        </ScatterChart>
                      </ChartContainer>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No risk/return data available for selected companies
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Summary Statistics */}
      {selectedCompanies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Summary Statistics</CardTitle>
            <CardDescription>
              Key statistics for selected companies
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatNumber(
                    selectedCompaniesData.reduce((sum, c) => sum + c.currentPrice, 0) / selectedCompaniesData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. Price</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatNumber(
                    selectedCompaniesData.reduce((sum, c) => sum + c.pe, 0) / selectedCompaniesData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. P/E</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatNumber(
                    selectedCompaniesData.reduce((sum, c) => sum + c.eps, 0) / selectedCompaniesData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. EPS</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatPercentage(
                    selectedCompaniesData.reduce((sum, c) => sum + c.dividendYield, 0) / selectedCompaniesData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. Dividend Yield</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}