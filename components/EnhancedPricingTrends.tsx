'use client'

import React, { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts"
import { TrendingUp, TrendingDown, BarChart3, Search, Loader2, RefreshCw } from "lucide-react"
import { formatN<PERSON>ber, formatPercentage } from "@/utils/dataHandler"

// Chart configuration for consistent styling
const chartConfig = {
  currentPrice: {
    label: "Current Price",
    color: "hsl(var(--chart-1))",
  },
  priceChange: {
    label: "Price Change %",
    color: "hsl(var(--chart-2))",
  },
  pe: {
    label: "P/E Ratio",
    color: "hsl(var(--chart-3))",
  },
  eps: {
    label: "EPS",
    color: "hsl(var(--chart-4))",
  },
  marketCap: {
    label: "Market Cap",
    color: "hsl(var(--chart-5))",
  },
}

// Color palette for multiple companies
const COMPANY_COLORS = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
  "hsl(var(--chart-5))",
  "#8B5CF6", // Purple
  "#F59E0B", // Amber
  "#EF4444", // Red
  "#10B981", // Emerald
  "#3B82F6", // Blue
]

interface DatabaseRecord {
  _id: string
  filename: string
  date: string
  uploadedAt: string
  data: {
    database: any[]
  }
}

interface CompanyTrendData {
  companyCode: string
  companyName: string
  trends: {
    date: string
    currentPrice: number
    priceChange?: number
    pe?: number
    eps?: number
    marketCap?: number
  }[]
}

export function EnhancedPricingTrends() {
  const [databaseRecords, setDatabaseRecords] = useState<DatabaseRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [activeChart, setActiveChart] = useState("price-trends")

  // Fetch recent database records summary from MongoDB (lightweight)
  const fetchDatabaseRecords = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use lightweight summary endpoint to avoid memory issues
      const response = await fetch('/api/get-database-summary?limit=5')
      if (!response.ok) {
        throw new Error('Failed to fetch database summary')
      }

      const data = await response.json()
      if (data.success && data.records) {
        setDatabaseRecords(data.records)
      } else {
        // Gracefully handle when no data is available
        setDatabaseRecords([])
        if (data.error) {
          console.warn('Database summary warning:', data.error)
        }
      }
    } catch (err) {
      console.error('Error fetching database records:', err)
      setError('Database temporarily unavailable. Large documents may cause memory issues.')
      setDatabaseRecords([]) // Set empty array as fallback
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDatabaseRecords()
  }, [])

  // Extract all available companies from the most recent record
  const availableCompanies = useMemo(() => {
    if (databaseRecords.length === 0) return []
    
    const mostRecentRecord = databaseRecords[0]
    if (!mostRecentRecord?.data?.database) return []
    
    return mostRecentRecord.data.database
      .filter((company: any) => 
        company?.Company && 
        company?.["Company code"] && 
        company?.["Current price"] !== null &&
        company?.["Current price"] !== undefined
      )
      .map((company: any) => ({
        name: company.Company,
        code: company["Company code"],
        currentPrice: parseFloat(company["Current price"]) || 0,
        sector: company.Sector || "Unknown",
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }, [databaseRecords])

  // Filter companies based on search term
  const filteredCompanies = useMemo(() => {
    return availableCompanies.filter(company => 
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.code.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [availableCompanies, searchTerm])

  // Generate trend data for selected companies
  const companyTrendData = useMemo(() => {
    if (selectedCompanies.length === 0 || databaseRecords.length === 0) return []

    const trendData: CompanyTrendData[] = []

    selectedCompanies.forEach(companyCode => {
      const companyTrends: CompanyTrendData['trends'] = []
      let companyName = companyCode

      // Sort records by date (oldest first for trend calculation)
      const sortedRecords = [...databaseRecords].sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
      )

      sortedRecords.forEach((record, index) => {
        const company = record.data.database?.find((c: any) =>
          c?.["Company code"] === companyCode
        )

        if (company) {
          if (index === 0) {
            companyName = company.Company || companyCode
          }

          const currentPrice = parseFloat(company["Current price"]) || 0
          const previousPrice = index > 0 ? companyTrends[index - 1]?.currentPrice : currentPrice
          const priceChange = previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0

          companyTrends.push({
            date: record.date,
            currentPrice,
            priceChange: index > 0 ? priceChange : 0,
            pe: parseFloat(company.PE) || 0,
            eps: parseFloat(company["Earning per share (Annual)"]) || 0,
            marketCap: parseFloat(company["Market cap/ Total Market cap"]) || 0,
          })
        }
      })

      if (companyTrends.length > 0) {
        trendData.push({
          companyCode,
          companyName,
          trends: companyTrends
        })
      }
    })

    return trendData
  }, [selectedCompanies, databaseRecords])

  // Prepare chart data by combining all company trends into a single dataset
  const chartData = useMemo(() => {
    if (companyTrendData.length === 0) return []

    // Get all unique dates
    const allDates = [...new Set(
      companyTrendData.flatMap(company => company.trends.map(trend => trend.date))
    )].sort()

    // Create combined data points for each date
    return allDates.map(date => {
      const dataPoint: any = { date }

      companyTrendData.forEach(company => {
        const trendForDate = company.trends.find(trend => trend.date === date)
        if (trendForDate) {
          dataPoint[`${company.companyCode}_price`] = trendForDate.currentPrice
          dataPoint[`${company.companyCode}_change`] = trendForDate.priceChange
          dataPoint[`${company.companyCode}_pe`] = trendForDate.pe
          dataPoint[`${company.companyCode}_eps`] = trendForDate.eps
        }
      })

      return dataPoint
    })
  }, [companyTrendData])

  // Handle company selection
  const toggleCompanySelection = (companyCode: string) => {
    setSelectedCompanies(prev => {
      if (prev.includes(companyCode)) {
        return prev.filter(code => code !== companyCode)
      } else if (prev.length < 8) { // Limit to 8 companies for readability
        return [...prev, companyCode]
      }
      return prev
    })
  }

  // Clear all selections
  const clearSelections = () => {
    setSelectedCompanies([])
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Enhanced Pricing Trends
          </CardTitle>
          <CardDescription>
            Loading pricing trend data from MongoDB...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Enhanced Pricing Trends
          </CardTitle>
          <CardDescription>
            Error loading pricing trend data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-red-600 dark:text-red-400">
            {error}
          </div>
          <div className="text-center">
            <Button onClick={fetchDatabaseRecords} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (databaseRecords.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Enhanced Pricing Trends
          </CardTitle>
          <CardDescription>
            No database records available from MongoDB. Please upload Excel files with company data first.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-4">
              <p className="text-lg mb-2">📊 Getting Started</p>
              <p className="text-sm">To use the Enhanced Pricing Trends feature:</p>
            </div>
            <div className="text-left max-w-md mx-auto space-y-2 text-sm text-muted-foreground">
              <p>1. Upload Excel files with company data to MongoDB</p>
              <p>2. Ensure files contain a "Data base" sheet with company information</p>
              <p>3. The system will automatically detect and analyze pricing trends</p>
              <p>4. You can then compare up to 8 companies across recent records</p>
            </div>
            <div className="mt-6">
              <Button onClick={fetchDatabaseRecords} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Check for Data
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Enhanced Pricing Trends
          </CardTitle>
          <CardDescription>
            Compare price changes from recent {databaseRecords.length} database records. 
            Select up to 8 companies to analyze historical pricing trends.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Selection Controls */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search Companies</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Selected Companies</Label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedCompanies.length}/8 selected
                </Badge>
                {selectedCompanies.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearSelections}>
                    Clear All
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={fetchDatabaseRecords}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {/* Company Selection Grid */}
          <div className="space-y-2">
            <Label>Available Companies ({filteredCompanies.length})</Label>
            <div className="max-h-60 overflow-y-auto border rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {filteredCompanies.map(company => (
                  <div
                    key={company.code}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedCompanies.includes(company.code)
                        ? "bg-primary/10 border-primary"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => toggleCompanySelection(company.code)}
                  >
                    <div className="font-medium text-sm">{company.name}</div>
                    <div className="text-xs text-muted-foreground">{company.code}</div>
                    <div className="text-xs text-muted-foreground">
                      Price: {formatNumber(company.currentPrice)} | Sector: {company.sector}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      {selectedCompanies.length > 0 && companyTrendData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Historical Pricing Analysis</CardTitle>
            <CardDescription>
              Visual comparison of selected companies across recent {databaseRecords.length} database records
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeChart} onValueChange={setActiveChart}>
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
                <TabsTrigger value="price-trends">Price Trends</TabsTrigger>
                <TabsTrigger value="price-changes">Price Changes</TabsTrigger>
                <TabsTrigger value="latest-comparison">Latest Comparison</TabsTrigger>
                <TabsTrigger value="performance-metrics">Performance</TabsTrigger>
              </TabsList>

              {/* Price Trends Line Chart */}
              <TabsContent value="price-trends" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Price Trends Over Time</CardTitle>
                    <CardDescription>
                      Historical price movements for selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[500px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis
                            dataKey="date"
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip
                            content={<ChartTooltipContent />}
                            formatter={(value, name) => [
                              formatNumber(value as number),
                              name.replace('_price', '').replace('_', ' ')
                            ]}
                          />
                          <ChartLegend content={<ChartLegendContent />} />
                          {companyTrendData.map((company, index) => (
                            <Line
                              key={company.companyCode}
                              type="monotone"
                              dataKey={`${company.companyCode}_price`}
                              stroke={COMPANY_COLORS[index % COMPANY_COLORS.length]}
                              strokeWidth={2}
                              dot={{ r: 4 }}
                              name={company.companyName}
                            />
                          ))}
                        </LineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Price Changes Bar Chart */}
              <TabsContent value="price-changes" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Price Change Percentage</CardTitle>
                    <CardDescription>
                      Percentage change in prices across all time periods
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[500px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis
                            dataKey="date"
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip
                            content={<ChartTooltipContent />}
                            formatter={(value, name) => [
                              `${formatNumber(value as number)}%`,
                              name.replace('_change', '').replace('_', ' ')
                            ]}
                          />
                          <ChartLegend content={<ChartLegendContent />} />
                          {companyTrendData.map((company, index) => (
                            <Bar
                              key={company.companyCode}
                              dataKey={`${company.companyCode}_change`}
                              fill={COMPANY_COLORS[index % COMPANY_COLORS.length]}
                              name={company.companyName}
                              radius={[2, 2, 0, 0]}
                            />
                          ))}
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Latest Price Comparison */}
              <TabsContent value="latest-comparison" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Latest Price Comparison</CardTitle>
                    <CardDescription>
                      Current prices from the most recent database record
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={companyTrendData.map(company => ({
                          name: company.companyName.length > 15 ? company.companyName.substring(0, 15) + '...' : company.companyName,
                          currentPrice: company.trends[company.trends.length - 1]?.currentPrice || 0,
                          code: company.companyCode
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis
                            dataKey="name"
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                            angle={-45}
                            textAnchor="end"
                            height={100}
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip
                            content={<ChartTooltipContent />}
                            formatter={(value) => [formatNumber(value as number), "Current Price"]}
                          />
                          <Bar
                            dataKey="currentPrice"
                            fill="var(--color-currentPrice)"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Performance Metrics */}
              <TabsContent value="performance-metrics" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics Comparison</CardTitle>
                    <CardDescription>
                      Latest P/E ratios and EPS values for selected companies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer config={chartConfig} className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={companyTrendData.map(company => {
                          const latestData = company.trends[company.trends.length - 1]
                          return {
                            name: company.companyName.length > 15 ? company.companyName.substring(0, 15) + '...' : company.companyName,
                            pe: latestData?.pe || 0,
                            eps: latestData?.eps || 0,
                            code: company.companyCode
                          }
                        })}>
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                          <XAxis
                            dataKey="name"
                            tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                            stroke="hsl(var(--muted-foreground))"
                            angle={-45}
                            textAnchor="end"
                            height={100}
                          />
                          <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <ChartLegend content={<ChartLegendContent />} />
                          <Bar dataKey="pe" fill="var(--color-pe)" name="P/E Ratio" />
                          <Bar dataKey="eps" fill="var(--color-eps)" name="EPS" />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Summary Statistics */}
      {selectedCompanies.length > 0 && companyTrendData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Summary Statistics</CardTitle>
            <CardDescription>
              Key statistics for selected companies based on latest data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatNumber(
                    companyTrendData.reduce((sum, company) => {
                      const latestPrice = company.trends[company.trends.length - 1]?.currentPrice || 0
                      return sum + latestPrice
                    }, 0) / companyTrendData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. Current Price</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold flex items-center justify-center gap-1">
                  {(() => {
                    const avgChange = companyTrendData.reduce((sum, company) => {
                      const latestChange = company.trends[company.trends.length - 1]?.priceChange || 0
                      return sum + latestChange
                    }, 0) / companyTrendData.length
                    return (
                      <>
                        {avgChange >= 0 ? (
                          <TrendingUp className="h-5 w-5 text-green-600" />
                        ) : (
                          <TrendingDown className="h-5 w-5 text-red-600" />
                        )}
                        {formatNumber(Math.abs(avgChange))}%
                      </>
                    )
                  })()}
                </div>
                <div className="text-sm text-muted-foreground">Avg. Price Change</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatNumber(
                    companyTrendData.reduce((sum, company) => {
                      const latestPE = company.trends[company.trends.length - 1]?.pe || 0
                      return sum + latestPE
                    }, 0) / companyTrendData.length
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Avg. P/E Ratio</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {databaseRecords.length}
                </div>
                <div className="text-sm text-muted-foreground">Database Records</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
