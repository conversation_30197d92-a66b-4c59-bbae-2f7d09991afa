"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { useDropzone } from "react-dropzone"
import { Upload, FileSpreadsheet, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { useData } from "@/contexts/DataContext"
import { parseExcelFile, parseExcelFileWithWorker } from "@/utils/dataHandler"
import { useToast } from "@/hooks/use-toast"

// Function to extract date from filename
function extractDateFromFilename(filename: string): string | null {
  // Match common date formats in filenames
  // Format: YYYY-MM-DD, YYYYMMDD, DD-MM-YYYY, MM-DD-YYYY, etc.
  const datePatterns = [
    /(?<date>\d{4}[-_.]\d{2}[-_.]\d{2})/, // YYYY-MM-DD, YYYY_MM_DD, YYYY.MM.DD
    /(?<date>\d{2}[-_.]\d{2}[-_.]\d{4})/, // DD-MM-YYYY, MM-DD-YYYY
    /(?<date>\d{8})/, // YYYYMMDD
  ];

  for (const pattern of datePatterns) {
    const match = filename.match(pattern);
    if (match && match.groups?.date) {
      return match.groups.date;
    } else if (match && match[0]) {
      return match[0];
    }
  }

  return null;
}

export function FileUpload() {
  const { data, setData, setIsLoading, setError, setFileName, setFileDate } = useData()
  const { toast } = useToast()
  const [uploadProgress, setUploadProgress] = useState(0)
  const [progressMessage, setProgressMessage] = useState('')

  // Show notification when data is loaded from localStorage
  useEffect(() => {
    if (data) {
      const hasStoredData = localStorage.getItem('excel-dashboard-data')
      if (hasStoredData) {
        toast({
          title: "Data restored",
          description: "Previously uploaded Excel data has been restored from local storage",
        })
      }
    }
  }, []) // Only run on mount

  // Function to clear stored data
  const clearStoredData = () => {
    setData(null)
    toast({
      title: "Data cleared",
      description: "All stored Excel data has been removed",
    })
  }

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0]
      if (!file) return

      if (!file.name.endsWith(".xlsx")) {
        setError("Please upload a valid .xlsx file")
        toast({
          title: "Invalid file type",
          description: "Please upload a .xlsx file",
          variant: "destructive",
        })
        return
      }

      setIsLoading(true)
      setError(null)
      setUploadProgress(0)
      setProgressMessage('Starting file upload...')

      try {
        // Store the filename
        setFileName(file.name)
        
        // Extract and store date from filename
        const extractedDate = extractDateFromFilename(file.name)
        if (extractedDate) {
          setFileDate(extractedDate)
        }
        
        setProgressMessage('Parsing Excel file...')
        
        // Try to use Web Worker for better performance
        let parsedData
        try {
          parsedData = await parseExcelFileWithWorker(file, (progress, message) => {
            setUploadProgress(progress)
            setProgressMessage(message || 'Processing...')
          })
        } catch (workerError) {
          // Fallback to main thread parsing
          setProgressMessage('Processing with fallback method...')
          parsedData = await parseExcelFile(file)
          setUploadProgress(80)
        }
        
        setData(parsedData)
        setUploadProgress(90)
        setProgressMessage('Saving to database...')
        
        // Save data to MongoDB
        try {
          const response = await fetch('/api/save-data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              data: parsedData,
              filename: file.name,
              uploadedBy: 'user' // You can modify this to use actual user identification
            })
          })
          
          const result = await response.json()
          
          if (result.success) {
            setUploadProgress(100)
            setProgressMessage('Upload completed successfully!')
            toast({
              title: "File uploaded successfully",
              description: `Your Excel data has been loaded, analyzed, and saved to database (ID: ${result.id})`,
            })
          } else {
            console.warn('Failed to save to database:', result.error)
            setUploadProgress(100)
            setProgressMessage('Upload completed (local only)')
            toast({
              title: "File uploaded (local only)",
              description: "Data loaded successfully but couldn't save to database. Data is available locally.",
              variant: "default",
            })
          }
        } catch (dbError) {
          console.warn('Database save error:', dbError)
          setUploadProgress(100)
          setProgressMessage('Upload completed (local only)')
          toast({
            title: "File uploaded (local only)",
            description: "Data loaded successfully but couldn't save to database. Data is available locally.",
            variant: "default",
          })
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to parse Excel file"
        setError(errorMessage)
        setUploadProgress(0)
        setProgressMessage('')
        toast({
          title: "Upload failed",
          description: errorMessage,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
        // Reset progress after a delay
        setTimeout(() => {
          setUploadProgress(0)
          setProgressMessage('')
        }, 3000)
      }
    },
    [setData, setIsLoading, setError, setFileName, setFileDate, toast],
  )

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
    },
    multiple: false,
    noClick: true, // Disable click on dropzone to fix MS Edge compatibility
    useFsAccessApi: false, // Disable File System Access API for better browser compatibility
  })

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-2xl p-8 text-center cursor-pointer transition-all duration-300 ${
        isDragActive
          ? "border-primary bg-gradient-to-br from-primary/10 via-accent/5 to-info/5 dark:from-primary/20 dark:via-accent/10 dark:to-info/10 shadow-colored scale-105"
          : "border-muted-foreground/25 dark:border-muted-foreground/40 hover:border-primary/50 dark:hover:border-primary/60 hover:bg-gradient-to-br hover:from-primary/5 hover:to-accent/5 dark:hover:from-primary/10 dark:hover:to-accent/10 hover:shadow-sm"
      }`}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center gap-6 animate-fade-in">
        <div className={`p-4 rounded-2xl transition-all duration-300 ${
          isDragActive
            ? "bg-gradient-to-br from-primary to-accent shadow-colored animate-bounce-subtle"
            : "bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20"
        }`}>
          {isDragActive ? (
            <Upload className="h-10 w-10 text-white" />
          ) : (
            <FileSpreadsheet className="h-10 w-10 text-primary dark:text-primary" />
          )}
        </div>
        <div className="space-y-2">
          <p className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent dark:from-primary dark:to-accent">
            {isDragActive ? "✨ Drop your Excel file here" : "📊 Drag & drop your Excel file here"}
          </p>
          <p className="text-base text-muted-foreground dark:text-muted-foreground">or click to browse files (.xlsx only)</p>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground dark:text-muted-foreground">
            <span className="w-2 h-2 rounded-full bg-success animate-pulse"></span>
            Supports multi-sheet Excel files
          </div>
        </div>
        <div className="flex gap-3">
          <Button variant="gradient" onClick={open} type="button" className="animate-scale-in shadow-colored">
            📁 Choose File
          </Button>
          {data && (
            <Button variant="outline" onClick={clearStoredData} type="button" className="text-destructive hover:text-destructive card-hover border-destructive/20 dark:border-destructive/30 dark:text-destructive dark:hover:bg-destructive/10">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Data
            </Button>
          )}
        </div>

        {/* Progress indicator */}
        {(uploadProgress > 0 || progressMessage) && (
          <div className="w-full max-w-md mx-auto space-y-3 animate-slide-up">
            <div className="p-4 rounded-xl bg-gradient-to-r from-primary/5 to-accent/5 dark:from-primary/10 dark:to-accent/10 border border-primary/20 dark:border-primary/30 shadow-sm dark:shadow-dark-elevated">
              <Progress value={uploadProgress} className="w-full h-2 bg-muted dark:bg-muted" />
              {progressMessage && (
                <p className="text-sm font-medium text-center mt-2 text-primary dark:text-primary">
                  {progressMessage}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
