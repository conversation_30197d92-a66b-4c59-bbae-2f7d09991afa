"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { BarChart3, Loader2, Download } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import {
  Bar,
  Bar<PERSON>hart,
  Line,
  LineChart,
  Pie,
  PieChart,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82CA9D"]

interface DiagramGeneratorProps {
  selectedCompany: string
  companyData: {
    database?: any
    dailyPrice?: any
    marketCap?: any
  }
}

// Helper function to parse numeric values consistently
const parseNumericValue = (value: any) => {
  if (value === null || value === undefined || value === "") return 0;
  // Handle both string and number inputs
  const stringValue = String(value);
  // Remove non-numeric characters except decimal point and minus sign
  const cleanedValue = stringValue.replace(/[^\d.-]/g, "");
  const parsedValue = Number.parseFloat(cleanedValue);
  return isNaN(parsedValue) ? 0 : parsedValue;
};

export function DiagramGenerator({ selectedCompany, companyData }: DiagramGeneratorProps) {
  const { toast } = useToast()
  const [chartType, setChartType] = useState<string>("")
  const [customPrompt, setCustomPrompt] = useState("")
  const [chartData, setChartData] = useState<any[]>([])
  const [chartConfig, setChartConfig] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const companyName = companyData.database?.["Company Name"] || selectedCompany

  const chartTypes = [
    { value: "performance", label: "Performance Chart" },
    { value: "financial", label: "Financial Metrics" },
    { value: "ratios", label: "Key Ratios" },
    { value: "trend", label: "Price Trend" }
  ]

  const generateChart = async () => {
    setIsLoading(true)

    try {
      if (chartType === "custom") {
        // AI-generated chart
        console.log("Generating custom AI chart for:", selectedCompany)
        
        if (!customPrompt.trim()) {
          throw new Error("Please provide a description for the chart you want to generate")
        }

        const response = await fetch("/api/generate-diagram", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt: customPrompt,
            companyCode: selectedCompany,
            companyData,
          }),
        })

        console.log("Diagram API response status:", response.status)

        if (!response.ok) {
          const errorData = await response.json()
          console.error("Diagram API error:", errorData)
          throw new Error(errorData.details || errorData.error || "Failed to generate chart")
        }

        const result = await response.json()

        // Validate the AI response structure
        if (!result || typeof result !== 'object') {
          throw new Error("Invalid response format from AI")
        }

        if (!result.data || !Array.isArray(result.data) || result.data.length === 0) {
          throw new Error("No chart data received from AI. Please try a different prompt.")
        }

        if (!result.config || !result.config.type || !result.config.title) {
          throw new Error("Invalid chart configuration received from AI")
        }
        
        // Validate and normalize the data
        const validatedData = result.data.map((item, index) => {
          // Ensure each item has a name property
          if (!item.name || typeof item.name !== 'string') {
            item.name = `Item ${index + 1}`;
          }
          
          // Ensure numeric values based on chart type
          if (result.config.type === 'line') {
            if (typeof item.price !== 'undefined') {
              item.price = parseNumericValue(item.price);
            } else if (typeof item.value !== 'undefined') {
              // Convert value to price for line charts
              item.price = parseNumericValue(item.value);
              delete item.value;
            } else {
              item.price = 0;
            }
          } else {
            if (typeof item.value !== 'undefined') {
              item.value = parseNumericValue(item.value);
            } else if (typeof item.price !== 'undefined') {
              // Convert price to value for bar/pie charts
              item.value = parseNumericValue(item.price);
              delete item.price;
            } else {
              item.value = 0;
            }
          }
          
          return item;
        }).filter(item => {
          // Filter out items with zero or invalid values
          const hasValidValue = result.config.type === 'line' 
            ? (item.price && item.price > 0)
            : (item.value && item.value > 0);
          return hasValidValue;
        });

        if (validatedData.length === 0) {
          throw new Error("No valid data points found. Please try a different prompt or check your data.")
        }

        // Enhance config with additional properties
        const enhancedConfig = {
          ...result.config,
          title: result.config.title || `${companyName} - AI Generated Chart`,
          description: result.config.description || 'AI-generated chart based on company data'
        };

        setChartData(validatedData)
        setChartConfig(enhancedConfig)
      } else {
        // Predefined chart types for specific company
        console.log("Generating predefined chart for:", selectedCompany)
        generateCompanyChart()
      }

      toast({
        title: "Chart generated",
        description: `Chart for ${companyName} has been created successfully`,
      })
    } catch (error) {
      console.error("Chart generation error:", error)

      const errorMessage = error instanceof Error ? error.message : "Failed to generate chart"

      toast({
        title: "Chart generation failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const generateCompanyChart = () => {
    switch (chartType) {
      case "performance":
        if (companyData.database) {
          const performanceData = [
            {
              name: "Current Price",
              value: parseNumericValue(companyData.database["Current price"]),
            },
            {
              name: "Net Asset per Share",
        value: parseNumericValue(companyData.database["Net asset per share"]),
            },
            {
              name: "EPS (Annual)",
              value: parseNumericValue(companyData.database["Earning Per share (Annual)"]),
            },
            {
              name: "Dividend Amount",
              value: parseNumericValue(companyData.database["Total Cash Dividend amount"]),
            },
          ].filter((item) => item.value > 0);
      
          setChartData(performanceData);
          setChartConfig({ type: "bar", title: `${companyName} - Key Performance Metrics` });
        }
        break;
      
      case "financial":
        if (companyData.database) {
          const financialData = [];
          if (companyData.database["ROE"])
            financialData.push({
              name: "ROE (%)",
              value: parseNumericValue(companyData.database["ROE"]) * 100,
            });
          if (companyData.database["ROA"])
            financialData.push({
              name: "ROA (%)",
              value: parseNumericValue(companyData.database["ROA"]) * 100,
            });
          if (companyData.database["PBV"])
            financialData.push({
              name: "PBV",
              value: parseNumericValue(companyData.database["PBV"]),
            });
          if (companyData.database["PE"])
            financialData.push({
              name: "PE Ratio",
              value: parseNumericValue(companyData.database["PE"]),
            });
          if (companyData.database["BETA VALUES AGAINST ASPI"])
            financialData.push({
              name: "Beta",
              value: parseNumericValue(companyData.database["BETA VALUES AGAINST ASPI"]),
            });
      
          setChartData(financialData.filter((item) => item.value > 0));
          setChartConfig({ type: "bar", title: `${companyName} - Financial Metrics` });
        }
        break;
      
      case "ratios":
        if (companyData.database) {
          const ratioData = [
            {
              name: "Debt to Equity",
              value: parseNumericValue(companyData.database["Debt to Equity (Giyaring)"]),
            },
            {
              name: "Debt Ratio",
              value: parseNumericValue(companyData.database["Debt reatio"]),
            },
            {
              name: "Dividend Yield",
        value: parseNumericValue(companyData.database["Dividend Yield"]) * 100,
            },
            {
              name: "PEG Ratio",
              value: parseNumericValue(companyData.database["PEG ratio (current)"]),
            },
          ].filter((item) => item.value > 0 && item.value < 1000); // Filter out extreme values
      
          setChartData(ratioData);
          setChartConfig({ type: "bar", title: `${companyName} - Key Financial Ratios` });
        }
        break;
      
      case "trend":
        if (companyData.database) {
          const revenueData = [];
          const years = ["2024", "2023", "2022", "2021", "2020"];
          
          years.forEach(year => {
            const revenueKey = `${year} Revenue/Sales/Gross written premiums`;
            const revenue = parseNumericValue(companyData.database[revenueKey]);
            if (revenue > 0) {
              revenueData.push({
                name: year,
                price: revenue / 1000000, // Convert to millions for readability
              });
            }
          });
          
          // If no revenue data, try quarterly data
          if (revenueData.length === 0) {
            const quarterlyData = [
              { name: "Q1", price: parseNumericValue(companyData.database["Q1N"]) },
              { name: "Q2", price: parseNumericValue(companyData.database["Q2N"]) },
              { name: "Q3", price: parseNumericValue(companyData.database["Q3N"]) },
              { name: "Q4", price: parseNumericValue(companyData.database["Q4N"]) },
            ].filter(item => item.price > 0);
            
            if (quarterlyData.length > 0) {
              setChartData(quarterlyData);
              setChartConfig({ type: "line", title: `${companyName} - Quarterly Price Trend` });
              return;
            }
          }
          
          if (revenueData.length > 0) {
            setChartData(revenueData.reverse()); // Show chronological order
            setChartConfig({ type: "line", title: `${companyName} - Revenue Trend (Millions)` });
          }
        }
        break;
    }
  };

  const renderChart = () => {
    if (!chartData.length || !chartConfig) return null;

    // Ensure all data values are properly formatted numbers
    const validatedData = chartData.map(item => {
      const result = {...item};
      if (typeof result.value !== 'undefined') {
        result.value = parseNumericValue(result.value);
      }
      if (typeof result.price !== 'undefined') {
        result.price = parseNumericValue(result.price);
      }
      return result;
    }).filter(item => {
      // Filter out invalid data points
      if (typeof item.value !== 'undefined' && item.value === 0) return false;
      if (typeof item.price !== 'undefined' && item.price === 0) return false;
      return true;
    });

    // If no valid data after filtering, show a message
    if (validatedData.length === 0) {
      return <div className="text-center py-8 text-muted-foreground">No valid data available for chart</div>;
    }

    const commonProps = {
      width: "100%",
      height: 400,
      data: validatedData,
    };

    // Custom tooltip formatter with better number formatting
    const formatTooltipValue = (value: number, label: string) => {
      // Format with 2 decimal places for small numbers, no decimals for large numbers
      const formattedValue = Math.abs(value) < 100 
        ? Number(value).toFixed(2) 
        : Number(value).toLocaleString('en-US', { maximumFractionDigits: 0 });
      return [formattedValue, label];
    };

    switch (chartConfig.type) {
      case "bar":
        return (
          <ResponsiveContainer {...commonProps}>
            <BarChart data={validatedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => formatTooltipValue(Number(value), "Value")} />
              <Legend />
              <Bar dataKey="value" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        );

      case "line":
        return (
          <ResponsiveContainer {...commonProps}>
            <LineChart data={validatedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => formatTooltipValue(Number(value), "Price")} />
              <Legend />
              <Line type="monotone" dataKey="price" stroke="#8884d8" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        );

      case "pie":
        return (
          <ResponsiveContainer {...commonProps}>
            <PieChart>
              <Pie
                data={validatedData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {validatedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatTooltipValue(Number(value), "Value")} />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Chart Generator for {companyName}
          </CardTitle>
          <CardDescription>
            Generate interactive charts and diagrams for {companyName} with complete company context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Chart Type</label>
              <Select value={chartType} onValueChange={setChartType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select chart type" />
                </SelectTrigger>
                <SelectContent>
                  {chartTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-muted-foreground">
                <p>
                  <strong>Company:</strong> {companyName}
                </p>
                <p>
                  <strong>Symbol:</strong> {selectedCompany}
                </p>
                <p>
                  <strong>Available Data:</strong>{" "}
                  {[
                    companyData.database && "Database",
                    companyData.dailyPrice && "Daily Price",
                    companyData.marketCap && "Market Cap",
                  ]
                    .filter(Boolean)
                    .join(", ")}
                </p>
              </div>
            </div>
          </div>

          <Button onClick={generateChart} disabled={isLoading || !chartType} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating Chart for {companyName}...
              </>
            ) : (
              `Generate Chart for ${companyName}`
            )}
          </Button>
        </CardContent>
      </Card>

      {chartData.length > 0 && chartConfig && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>{chartConfig.title}</CardTitle>
                {chartConfig.description && (
                  <CardDescription className="mt-2">
                    {chartConfig.description}
                  </CardDescription>
                )}
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p><strong>Data Points:</strong> {chartData.length}</p>
              <p><strong>Chart Type:</strong> {chartConfig.type.charAt(0).toUpperCase() + chartConfig.type.slice(1)}</p>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-96">{renderChart()}</div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
