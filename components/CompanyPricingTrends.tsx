import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts"
import { TrendingUp, TrendingDown, Calendar, Search } from "lucide-react"
import { formatNumber, formatPercentage } from "@/utils/dataHandler"

// Chart configuration for consistent styling
const chartConfig = {
  currentPrice: {
    label: "Current Price",
    color: "hsl(var(--chart-1))",
  },
  pe: {
    label: "P/E Ratio",
    color: "hsl(var(--chart-2))",
  },
  pbv: {
    label: "P/B Ratio",
    color: "hsl(var(--chart-3))",
  },
  eps: {
    label: "EPS",
    color: "hsl(var(--chart-4))",
  },
  dividendYield: {
    label: "Dividend Yield",
    color: "hsl(var(--chart-5))",
  },
  marketCap: {
    label: "Market Cap",
    color: "hsl(var(--chart-6))",
  },
}

import { DatabaseRecord } from "@/utils/clientDataLoader"

interface CompanyPricingTrendsProps {
  allDatabaseRecords: DatabaseRecord[]
  companyCode: string
}

export function CompanyPricingTrends({ allDatabaseRecords, companyCode }: CompanyPricingTrendsProps) {
  const [activeChart, setActiveChart] = useState("price-trend")

  // Get company details from the first available record
  const companyDetails = useMemo(() => {
    for (const record of allDatabaseRecords) {
      if (record.data?.database) {
        const company = record.data.database.find((c: any) => 
          c?.["Company code"] === companyCode
        )
        if (company) {
          return {
            name: company.Company,
            code: company["Company code"],
            sector: company.Sector || "Unknown"
          }
        }
      }
    }
    return null
  }, [allDatabaseRecords, companyCode])

  // Get pricing trend data for the company
  const companyTrendData = useMemo(() => {
    if (!companyCode) return []
    
    const trendData: any[] = []
    
    allDatabaseRecords.forEach(record => {
      if (record.data?.database) {
        const company = record.data.database.find((c: any) => 
          c?.["Company code"] === companyCode
        )
        
        if (company) {
          trendData.push({
            date: record.date,
            filename: record.filename,
            currentPrice: parseFloat(company["Current price"]) || 0,
            pe: parseFloat(company.PE) || 0,
            pbv: parseFloat(company.PBV) || 0,
            eps: parseFloat(company["Earning per share (Annual)"]) || 0,
            dividendYield: (parseFloat(company["Dividend yield"]) || 0) * 100,
            marketCap: parseFloat(company["Market cap/ Total Market cap"]) || 0,
            beta: parseFloat(company["BETA VALUES AGAINST ASPI"]) || 0,
            roe: parseFloat(company.ROE) || 0,
            roa: parseFloat(company.ROA) || 0,
            netAssetPerShare: parseFloat(company["Net Asset per share"]) || 0,
          })
        }
      }
    })
    
    // Sort by date
    return trendData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }, [companyCode, allDatabaseRecords])

  // Calculate trend statistics
  const trendStats = useMemo(() => {
    if (companyTrendData.length < 2) return null
    
    const first = companyTrendData[0]
    const last = companyTrendData[companyTrendData.length - 1]
    
    const priceChange = last.currentPrice - first.currentPrice
    const priceChangePercent = (priceChange / first.currentPrice) * 100
    
    return {
      priceChange,
      priceChangePercent,
      isPositive: priceChange >= 0,
      firstDate: first.date,
      lastDate: last.date,
      dataPoints: companyTrendData.length
    }
  }, [companyTrendData])

  if (!allDatabaseRecords || allDatabaseRecords.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Company Pricing Trends</CardTitle>
          <CardDescription>
            No database records available. Please ensure multiple database files are loaded.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Company Pricing Trends
          </CardTitle>
          <CardDescription>
            Analyze pricing trends and financial metrics for a specific company across different time periods.
            Available data from {allDatabaseRecords.length} different dates.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Company Info and Trend Stats */}
          {companyDetails && trendStats && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-sm font-medium text-muted-foreground">Company</div>
                  <div className="text-lg font-semibold">{companyDetails.name}</div>
                  <div className="text-sm text-muted-foreground">{companyDetails.sector}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="text-sm font-medium text-muted-foreground">Price Change</div>
                  <div className={`text-lg font-semibold flex items-center gap-1 ${
                    trendStats.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {trendStats.isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                    {formatNumber(trendStats.priceChange)} ({formatPercentage(trendStats.priceChangePercent)})
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {trendStats.firstDate} to {trendStats.lastDate}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="text-sm font-medium text-muted-foreground">Data Points</div>
                  <div className="text-lg font-semibold">{trendStats.dataPoints}</div>
                  <div className="text-sm text-muted-foreground">Available records</div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Charts */}
      {companyCode && companyTrendData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Trend Analysis</CardTitle>
            <CardDescription>
              Historical data trends for {companyDetails?.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeChart} onValueChange={setActiveChart}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="price-trend">Price Trend</TabsTrigger>
                <TabsTrigger value="valuation">Valuation</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="dividends">Dividends</TabsTrigger>
              </TabsList>
              
              <TabsContent value="price-trend" className="space-y-4">
                <ChartContainer config={chartConfig} className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={companyTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                        stroke="hsl(var(--muted-foreground))"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Area
                        type="monotone"
                        dataKey="currentPrice"
                        stroke={chartConfig.currentPrice.color}
                        fill={chartConfig.currentPrice.color}
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </TabsContent>
              
              <TabsContent value="valuation" className="space-y-4">
                <ChartContainer config={chartConfig} className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={companyTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                        stroke="hsl(var(--muted-foreground))"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Line
                        type="monotone"
                        dataKey="pe"
                        stroke={chartConfig.pe.color}
                        strokeWidth={2}
                        dot={{ r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="pbv"
                        stroke={chartConfig.pbv.color}
                        strokeWidth={2}
                        dot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </TabsContent>
              
              <TabsContent value="performance" className="space-y-4">
                <ChartContainer config={chartConfig} className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={companyTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                        stroke="hsl(var(--muted-foreground))"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      <Bar dataKey="eps" fill={chartConfig.eps.color} />
                      <Bar dataKey="roe" fill={chartConfig.pe.color} />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </TabsContent>
              
              <TabsContent value="dividends" className="space-y-4">
                <ChartContainer config={chartConfig} className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={companyTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted-foreground))" opacity={0.3} />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                        stroke="hsl(var(--muted-foreground))"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} stroke="hsl(var(--muted-foreground))" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Area
                        type="monotone"
                        dataKey="dividendYield"
                        stroke={chartConfig.dividendYield.color}
                        fill={chartConfig.dividendYield.color}
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
      
      {companyCode && companyTrendData.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              No data available for this company across the loaded database records.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}