"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Brain, Loader2, <PERSON>rkles, Zap } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

interface AIAnalysisProps {
  selectedCompany: string
  companyData: {
    database?: any
    dailyPrice?: any
    marketCap?: any
  }
}

export function AIAnalysis({ selectedCompany, companyData }: AIAnalysisProps) {
  const { toast } = useToast()
  const [analysisType, setAnalysisType] = useState<string>("")
  const [customPrompt, setCustomPrompt] = useState("")
  const [result, setResult] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [useStreaming, setUseStreaming] = useState(true)
  const [streamingStatus, setStreamingStatus] = useState<string>("")
  const abortControllerRef = useRef<AbortController | null>(null)

  const companyName = companyData.database?.["Company Name"] || selectedCompany

  const analysisTypes = [
    {
      value: "summary",
      label: "Company Summary",
      prompt: "Provide a comprehensive summary of this company's performance and position",
    },
    {
      value: "financial",
      label: "Financial Analysis",
      prompt: "Analyze the financial health and key metrics of this company",
    },
    {
      value: "performance",
      label: "Performance Analysis",
      prompt: "Evaluate the stock performance and market position of this company",
    },
    { value: "swot", label: "SWOT Analysis", prompt: "Perform a detailed SWOT analysis for this company" },
    {
      value: "valuation",
      label: "Valuation Analysis",
      prompt: "Assess the valuation and investment potential of this company",
    },
    {
      value: "risk",
      label: "Risk Assessment",
      prompt: "Analyze the risk factors and potential concerns for this company",
    },
    { value: "custom", label: "Custom Analysis", prompt: "" },
  ]

  const handleStreamingAnalysis = async (prompt: string) => {
    setIsLoading(true)
    setResult("")
    setStreamingStatus("Initializing...")

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController()

    try {
      console.log("Sending streaming analysis request for company:", selectedCompany)

      const response = await fetch("/api/ai-analysis-stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          companyCode: selectedCompany,
          companyData,
        }),
        signal: abortControllerRef.current.signal,
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("No response body reader available")
      }

      const decoder = new TextDecoder()
      let buffer = ""

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split("\n")
        buffer = lines.pop() || ""

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6))
              
              switch (data.type) {
                case 'status':
                  setStreamingStatus(data.message)
                  break
                case 'chunk':
                  setResult(prev => prev + data.content)
                  break
                case 'complete':
                  setStreamingStatus("Analysis completed!")
                  toast({
                    title: "Analysis completed",
                    description: "AI analysis has been generated successfully",
                  })
                  break
                case 'error':
                  throw new Error(data.error)
              }
            } catch (parseError) {
              console.warn("Failed to parse SSE data:", line)
            }
          }
        }
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        setStreamingStatus("Analysis cancelled")
        return
      }
      
      console.error("Streaming analysis error:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to generate analysis"
      
      toast({
        title: "Analysis failed",
        description: errorMessage,
        variant: "destructive",
      })
      setStreamingStatus("Analysis failed")
    } finally {
      setIsLoading(false)
      abortControllerRef.current = null
      setTimeout(() => setStreamingStatus(""), 3000)
    }
  }

  const handleRegularAnalysis = async (prompt: string) => {
    setIsLoading(true)
    setResult("")

    try {
      console.log("Sending analysis request for company:", selectedCompany)

      const response = await fetch("/api/ai-analysis", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          companyCode: selectedCompany,
          companyData,
        }),
      })

      console.log("Response status:", response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API error:", errorData)
        throw new Error(errorData.details || errorData.error || "Failed to get AI analysis")
      }

      const { analysis } = await response.json()

      if (!analysis || analysis.trim() === "") {
        throw new Error("Empty analysis received from AI")
      }

      setResult(analysis)

      toast({
        title: "Analysis completed",
        description: "AI analysis has been generated successfully",
      })
    } catch (error) {
      console.error("Analysis error:", error)

      const errorMessage = error instanceof Error ? error.message : "Failed to generate analysis"

      toast({
        title: "Analysis failed",
        description: errorMessage,
        variant: "destructive",
      })

      setResult(
        `Analysis failed: ${errorMessage}\n\nPlease check your internet connection and try again. If the problem persists, the AI service may be temporarily unavailable.`,
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleAnalysis = async () => {
    const selectedType = analysisTypes.find((type) => type.value === analysisType)
    const prompt = analysisType === "custom" ? customPrompt : selectedType?.prompt

    if (!prompt) {
      toast({
        title: "Missing prompt",
        description: "Please select an analysis type or enter a custom prompt",
        variant: "destructive",
      })
      return
    }

    if (useStreaming) {
      await handleStreamingAnalysis(prompt)
    } else {
      await handleRegularAnalysis(prompt)
    }
  }

  const handleCancelAnalysis = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setIsLoading(false)
      setStreamingStatus("Analysis cancelled")
      setTimeout(() => setStreamingStatus(""), 3000)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI-Powered Analysis for {companyName}
          </CardTitle>
          <CardDescription>
            Get intelligent insights about {companyName} using AI analysis with complete company context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Analysis Type</label>
              <Select value={analysisType} onValueChange={setAnalysisType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select analysis type" />
                </SelectTrigger>
                <SelectContent>
                  {analysisTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-muted-foreground">
                <p>
                  <strong>Company:</strong> {companyName}
                </p>
                <p>
                  <strong>Symbol:</strong> {selectedCompany}
                </p>
                <p>
                  <strong>Data Available:</strong>{" "}
                  {[
                    companyData.database && "Database",
                    companyData.dailyPrice && "Daily Price",
                    companyData.marketCap && "Market Cap",
                  ]
                    .filter(Boolean)
                    .join(", ")}
                </p>
              </div>
            </div>
          </div>

          {analysisType === "custom" && (
            <div>
              <label className="text-sm font-medium mb-2 block">Custom Prompt</label>
              <Textarea
                placeholder="Enter your custom analysis prompt..."
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={3}
              />
            </div>
          )}

          {/* Streaming Toggle */}
          <div className="flex items-center space-x-2 p-3 bg-muted/50 rounded-lg">
            <Switch
              id="streaming-mode"
              checked={useStreaming}
              onCheckedChange={setUseStreaming}
              disabled={isLoading}
            />
            <Label htmlFor="streaming-mode" className="flex items-center gap-2 cursor-pointer">
              {useStreaming ? (
                <>
                  <Zap className="h-4 w-4 text-blue-500" />
                  Real-time Streaming
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 text-purple-500" />
                  Standard Analysis
                </>
              )}
            </Label>
            <span className="text-xs text-muted-foreground ml-auto">
              {useStreaming ? "See results as they generate" : "Wait for complete analysis"}
            </span>
          </div>

          {/* Streaming Status */}
          {streamingStatus && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground bg-blue-50 dark:bg-blue-950/20 p-2 rounded">
              <Loader2 className="h-3 w-3 animate-spin" />
              {streamingStatus}
            </div>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={handleAnalysis} 
              disabled={isLoading || !analysisType} 
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {useStreaming ? "Streaming..." : "Analyzing..."}
                </>
              ) : (
                <>
                  {useStreaming ? <Zap className="h-4 w-4 mr-2" /> : <Sparkles className="h-4 w-4 mr-2" />}
                  Generate Analysis
                </>
              )}
            </Button>
            
            {isLoading && useStreaming && (
              <Button 
                onClick={handleCancelAnalysis}
                variant="outline"
                size="default"
              >
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Analysis Result for {companyName}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <div className="markdown-content">
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]}
                  components={{
                    // Custom styling for markdown elements
                    h1: ({ children }) => <h1 className="text-2xl font-bold mb-4 text-foreground">{children}</h1>,
                    h2: ({ children }) => <h2 className="text-xl font-semibold mb-3 text-foreground">{children}</h2>,
                    h3: ({ children }) => <h3 className="text-lg font-medium mb-2 text-foreground">{children}</h3>,
                    p: ({ children }) => <p className="mb-3 text-foreground leading-relaxed">{children}</p>,
                    ul: ({ children }) => <ul className="list-disc pl-6 mb-3 space-y-1">{children}</ul>,
                    ol: ({ children }) => <ol className="list-decimal pl-6 mb-3 space-y-1">{children}</ol>,
                    li: ({ children }) => <li className="text-foreground">{children}</li>,
                    strong: ({ children }) => <strong className="font-semibold text-foreground">{children}</strong>,
                    em: ({ children }) => <em className="italic text-foreground">{children}</em>,
                    code: ({ children }) => <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                    pre: ({ children }) => <pre className="bg-muted p-4 rounded-lg overflow-x-auto mb-3">{children}</pre>,
                    blockquote: ({ children }) => <blockquote className="border-l-4 border-primary pl-4 italic mb-3">{children}</blockquote>,
                  }}
                >
                  {result}
                </ReactMarkdown>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
