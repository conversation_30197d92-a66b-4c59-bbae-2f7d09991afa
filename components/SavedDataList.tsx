"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Trash2, Download, Calendar, FileSpreadsheet, Database } from "lucide-react"
import { useData } from "@/contexts/DataContext"
import { useToast } from "@/hooks/use-toast"
import { formatDistanceToNow } from "date-fns"

interface SavedDocument {
  _id: string
  filename: string
  data: any
  uploadedBy?: string
  uploadedAt: string
  metadata: {
    sheets: string[]
    totalRecords: number
  }
}

/**
 * Component to display and manage saved Excel data from MongoDB
 */
export function SavedDataList() {
  const [savedFiles, setSavedFiles] = useState<SavedDocument[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { setData, setFileName, setFileDate } = useData()
  const { toast } = useToast()

  /**
   * Fetch saved files metadata from MongoDB (lightweight)
   */
  const fetchSavedFiles = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use the lightweight metadata endpoint to avoid memory issues
      const response = await fetch('/api/get-files-metadata?limit=20&skip=0')
      const result = await response.json()

      if (result.success) {
        // Transform metadata to match expected format
        const transformedFiles = result.documents.map((doc: any) => ({
          ...doc,
          data: null, // Don't load full data for listing
          metadata: doc.metadata || { sheets: [], totalRecords: 0 }
        }))
        setSavedFiles(transformedFiles)
      } else {
        setError(result.error || 'Database temporarily unavailable. Large documents may cause memory issues.')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection failed'
      setError(`Failed to load saved files: ${errorMessage}`)
      console.error('Error fetching saved files:', err)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Load a saved file into the current session
   */
  const loadSavedFile = async (document: SavedDocument) => {
    try {
      // If data is not loaded (from metadata endpoint), fetch full document
      if (!document.data) {
        const response = await fetch(`/api/get-data?id=${document._id}`)
        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || 'Failed to load file data')
        }

        document = result.document
      }

      setData(document.data)
      setFileName(document.filename)

      // Extract date from filename if available
      const dateMatch = document.filename.match(/\d{4}[-_.]\d{2}[-_.]\d{2}/)
      if (dateMatch) {
        setFileDate(dateMatch[0])
      }

      toast({
        title: "Data loaded",
        description: `Successfully loaded ${document.filename} from database`,
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      toast({
        title: "Load failed",
        description: `Failed to load the saved file: ${errorMessage}`,
        variant: "destructive",
      })
    }
  }

  /**
   * Delete a saved file from MongoDB
   */
  const deleteSavedFile = async (id: string, filename: string) => {
    if (!confirm(`Are you sure you want to delete "${filename}"? This action cannot be undone.`)) {
      return
    }
    
    try {
      const response = await fetch(`/api/delete-data`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id })
      })
      
      const result = await response.json()
      
      if (result.success) {
        setSavedFiles(prev => prev.filter(file => file._id !== id))
        toast({
          title: "File deleted",
          description: `Successfully deleted ${filename}`,
        })
      } else {
        toast({
          title: "Delete failed",
          description: result.error || "Failed to delete file",
          variant: "destructive",
        })
      }
    } catch (err) {
      toast({
        title: "Delete failed",
        description: "An error occurred while deleting the file",
        variant: "destructive",
      })
    }
  }

  // Load saved files on component mount
  useEffect(() => {
    fetchSavedFiles()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Saved Files
          </CardTitle>
          <CardDescription>Loading saved Excel files from database...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Saved Files
          </CardTitle>
          <CardDescription>Error loading saved files</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-destructive mb-4">{error}</p>
            <Button onClick={fetchSavedFiles} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Saved Files ({savedFiles.length})
        </CardTitle>
        <CardDescription>
          Previously uploaded Excel files saved in the database
        </CardDescription>
      </CardHeader>
      <CardContent>
        {savedFiles.length === 0 ? (
          <div className="text-center py-8">
            <FileSpreadsheet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">No saved files found</p>
            <p className="text-sm text-muted-foreground">
              Upload an Excel file to see it saved here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {savedFiles.map((file) => (
              <div
                key={file._id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <FileSpreadsheet className="h-4 w-4 text-primary" />
                    <span className="font-medium">{file.filename}</span>
                    <Badge variant="secondary">
                      {file.metadata.totalRecords} records
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDistanceToNow(new Date(file.uploadedAt), { addSuffix: true })}
                    </div>
                    <div>
                      Sheets: {file.metadata.sheets.join(', ')}
                    </div>
                    {file.uploadedBy && (
                      <div>
                        By: {file.uploadedBy}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadSavedFile(file)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Load
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteSavedFile(file._id, file.filename)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            <div className="flex justify-center pt-4">
              <Button variant="outline" onClick={fetchSavedFiles}>
                Refresh
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}