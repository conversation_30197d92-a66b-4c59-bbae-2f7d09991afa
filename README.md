# Excel Dashboard

A modern web application for analyzing and visualizing Excel data with AI-powered insights.

## 🚀 Features

- **Excel File Upload**: Drag and drop or browse to upload Excel (.xlsx) files
- **Multi-Sheet Support**: Seamlessly switch between different sheets in your Excel file
- **Enhanced Pricing Trends**: Compare price changes across recent 15 database records with interactive charts
- **MongoDB Integration**: Save and load Excel data from MongoDB for persistent storage
- **Data Persistence**: Automatically saves your data to local storage for quick access
- **Responsive Design**: Works on desktop and mobile devices
- **AI-Powered Analysis**: Get intelligent insights from your data using AI

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui, Recharts for data visualization
- **Data Handling**: XLSX.js for Excel file processing
- **Database**: MongoDB for persistent data storage
- **AI Integration**: Google Gemini API
- **State Management**: React Context API

## 🚀 Getting Started

### Prerequisites

- Node.js 18.0 or later
- npm or pnpm
- Google Gemini API key (for AI features)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/excel-dashboard.git
   cd excel-dashboard
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create a `.env.local` file in the root directory and add your Gemini API key:

   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. Run the development server:

   ```bash
   npm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎯 Features in Detail

### 📊 Data Visualization

- Interactive data tables with sorting and search functionality
- Responsive design that works on all device sizes
- Real-time data updates

### 🤖 AI-Powered Analysis

- Generate comprehensive reports from your data
- Get insights and recommendations
- Custom analysis with natural language prompts

### 🔄 Data Management

- Upload and process Excel files
- Automatic data validation and error handling
- Persistent storage with local caching

## 🛠️ Development

### Available Scripts

- `npm dev` - Start the development server
- `npm build` - Build for production
- `npm start` - Start the production server
- `npm lint` - Run ESLint
- `npm format` - Format code with Prettier

### Environment Variables

Create a `.env.local` file with the following variables:

```
GEMINI_API_KEY=your_gemini_api_key_here
```


