# Enhanced Pricing Trends Feature

## Overview

The Enhanced Pricing Trends feature provides comprehensive analysis of price changes across multiple companies using historical data from MongoDB. This feature compares price movements from the recent 15 database records and visualizes them as interactive charts.

## Key Features

### 1. Historical Price Analysis
- Fetches the most recent 15 database records from MongoDB
- Compares price changes across all available records
- Calculates percentage price changes between time periods
- Supports up to 8 companies for optimal chart readability

### 2. Interactive Visualizations
- **Price Trends**: Line chart showing historical price movements over time
- **Price Changes**: Bar chart displaying percentage price changes for each time period
- **Latest Comparison**: Bar chart comparing current prices from the most recent record
- **Performance Metrics**: Bar chart showing P/E ratios and EPS values

### 3. Company Selection
- Search functionality to find companies by name or code
- Easy selection/deselection of companies for comparison
- Real-time filtering and search capabilities
- Clear visual indicators for selected companies

### 4. MongoDB Integration
- New API endpoint: `/api/pricing-trends-data`
- Fetches recent database records with pricing data
- Optimized queries for performance
- Error handling and loading states

## Technical Implementation

### New Components
- `EnhancedPricingTrends.tsx` - Main component for pricing trends analysis
- Enhanced MongoDB utilities in `lib/mongodb.ts`
- New API route: `app/api/pricing-trends-data/route.ts`

### Database Structure
The feature expects MongoDB documents with the following structure:
```json
{
  "_id": "document_id",
  "filename": "Stock market Database 2025 DD-MM-YYYY.xlsx",
  "uploadedAt": "2025-01-11T10:00:00.000Z",
  "data": {
    "database": [
      {
        "Company": "Company Name",
        "Company code": "COMP.N0000",
        "Current price": 123.45,
        "PE": 15.2,
        "Earning per share (Annual)": 8.1,
        "Market cap/ Total Market cap": 1000000
      }
    ]
  }
}
```

### API Endpoints

#### GET `/api/pricing-trends-data`
Fetches recent database records for pricing trends analysis.

**Query Parameters:**
- `limit` (optional): Maximum number of records to return (default: 15, max: 30)

**Response:**
```json
{
  "success": true,
  "records": [
    {
      "_id": "document_id",
      "filename": "filename.xlsx",
      "date": "2025-01-11",
      "uploadedAt": "2025-01-11T10:00:00.000Z",
      "data": {
        "database": [...]
      }
    }
  ],
  "count": 15
}
```

## Usage

### Accessing the Feature
1. Upload Excel files with company data to MongoDB
2. Navigate to the main dashboard
3. Click on the "Pricing Trends" tab
4. The Enhanced Pricing Trends component will automatically load

### Using the Interface
1. **Search Companies**: Use the search box to find specific companies
2. **Select Companies**: Click on company cards to select/deselect them
3. **View Charts**: Switch between different chart types using the tabs
4. **Refresh Data**: Use the refresh button to reload the latest data

### Chart Types
- **Price Trends**: Shows how prices have changed over time for each selected company
- **Price Changes**: Displays percentage changes between consecutive time periods
- **Latest Comparison**: Compares current prices across selected companies
- **Performance Metrics**: Shows P/E ratios and EPS values for performance analysis

## Configuration

### Environment Variables
Ensure your `.env.local` file contains:
```env
MONGODBCONNECTION=your_mongodb_connection_string
```

### Sheet Configuration
The feature is automatically available when database records are present. It's configured in `utils/constants.ts`:
```typescript
export const SHEET_CONFIGS = [
  { name: "Data base", key: "database" },
  { name: "Daily price Update", key: "dailyPrice" },
  { name: "Daily Market Cap", key: "marketCap" },
  { name: "Pricing Trends", key: "pricingTrends" }, // New addition
]
```

## Error Handling

The feature includes comprehensive error handling:
- MongoDB connection errors
- API request failures
- Data parsing errors
- Loading states with user feedback
- Retry functionality

## Performance Considerations

- Limits to 15 recent records by default for optimal performance
- Company selection limited to 8 companies for chart readability
- Efficient MongoDB queries with proper indexing
- Client-side caching of fetched data
- Debounced search functionality

## Future Enhancements

Potential improvements for future versions:
- Date range selection for custom time periods
- Export functionality for charts and data
- Additional chart types (candlestick, volume analysis)
- Real-time data updates
- Advanced filtering options (by sector, market cap, etc.)
- Comparison with market indices
