# MongoDB Integration for Excel Dashboard

This document describes the MongoDB integration features added to the Excel Dashboard application.

## Overview

The application now includes full MongoDB integration to persist uploaded Excel data, allowing users to:

- Save uploaded Excel files to a MongoDB database
- View previously saved files
- Load saved files back into the dashboard
- Delete saved files from the database

## Environment Setup

Ensure your `.env.local` file contains the MongoDB connection string:

```env
MONGODBCONNECTION=coonectionstringgoeshere
```

## Database Structure

### Database: `exceldashboard`

### Collection: `excel_uploads`

Each document in the collection contains:

```typescript
{
  _id: ObjectId,
  filename: string,           // Original Excel filename
  data: object,              // Parsed Excel data (all sheets)
  uploadedBy: string,        // User identifier
  uploadedAt: Date,          // Upload timestamp
  metadata: {
    sheets: string[],        // List of sheet names
    totalRecords: number     // Total number of records across all sheets
  }
}
```

## New Components

### 1. MongoDB Utility (`lib/mongodb.ts`)

Provides database connection and CRUD operations:

- `getDatabase()` - Get database instance
- `getCollection()` - Get collection instance
- `saveExcelData()` - Save Excel data to database
- `getUploadedFiles()` - Retrieve saved files
- `getUploadedFileById()` - Get specific file by ID
- `deleteUploadedFile()` - Delete file from database

### 2. API Endpoints

#### POST `/api/save-data`

Saves Excel data to MongoDB

**Request Body:**

```json
{
  "data": { /* parsed Excel data */ },
  "filename": "example.xlsx",
  "uploadedBy": "user"
}
```

**Response:**

```json
{
  "success": true,
  "id": "document_id",
  "message": "Data saved successfully",
  "metadata": {
    "sheets": ["Data base", "Daily price Update", "Daily Market Cap"],
    "totalRecords": 1500
  }
}
```

#### GET `/api/get-data`

Retrieves saved files from MongoDB

**Query Parameters:**

- `id` - Get specific document by ID
- `limit` - Maximum number of documents (default: 10)
- `skip` - Number of documents to skip (default: 0)

**Response:**

```json
{
  "success": true,
  "documents": [ /* array of saved documents */ ],
  "count": 5
}
```

#### DELETE `/api/delete-data`

Deletes a saved file from MongoDB

**Request Body:**

```json
{
  "id": "document_id"
}
```

### 3. SavedDataList Component

Displays and manages saved Excel files:

- Lists all saved files with metadata
- Shows upload date, file size, and sheet information
- Allows loading saved files back into the dashboard
- Provides delete functionality with confirmation
- Auto-refreshes the list

## Updated Features

### Enhanced File Upload

The `FileUpload` component now:

1. Parses Excel file locally (existing functionality)
2. Automatically saves data to MongoDB
3. Shows success/failure notifications
4. Falls back gracefully if database save fails

### Main Page Layout

When no data is loaded, the main page now shows:

1. File upload section
2. Saved files list (from MongoDB)

Users can either upload a new file or load a previously saved one.

## Error Handling

- **Database Connection Errors**: Graceful fallback to local-only mode
- **Save Failures**: Data remains available locally, user is notified
- **Load Failures**: Clear error messages with retry options
- **Delete Failures**: Confirmation dialogs and error notifications

## Security Considerations

- MongoDB connection string is stored in environment variables
- No sensitive data is logged to console
- User input is validated before database operations
- Proper error handling prevents information leakage

## Usage Examples

### Uploading a File

1. Drag and drop or select an Excel file
2. File is parsed and displayed in the dashboard
3. Data is automatically saved to MongoDB
4. Success notification shows the database document ID

### Loading a Saved File

1. View the "Saved Files" section on the main page
2. Click "Load" on any saved file
3. Data is loaded into the current session
4. Dashboard displays the loaded data

### Managing Saved Files

1. View file metadata (upload date, sheets, record count)
2. Delete unwanted files with confirmation
3. Refresh the list to see latest saves

## Development Notes

- MongoDB client connection is reused in development mode
- Production mode creates new connections as needed
- All database operations are asynchronous
- Error logging helps with debugging
- TypeScript interfaces ensure type safety

## Future Enhancements

- User authentication and file ownership
- File sharing and collaboration features
- Advanced search and filtering
- Data versioning and history
- Bulk operations (export, backup)
- Analytics on usage patterns
