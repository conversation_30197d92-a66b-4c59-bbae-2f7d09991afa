name: Build and Backup on Success

# Trigger the workflow on push to main branch or pull request
on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

# Define environment variables
env:
  NODE_VERSION: '18'
  BACKUP_BRANCH_PREFIX: 'backup'

jobs:
  build-and-backup:
    runs-on: ubuntu-latest
    
    steps:
    # Checkout the repository code
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for backup branch creation
        token: ${{ secrets.GITHUB_TOKEN }}
    
    # Setup Node.js environment
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    # Install dependencies
    - name: Install dependencies
      run: npm ci
    
    # Run linting (optional)
    - name: Run ESLint
      run: npm run lint --if-present
      continue-on-error: true
    
    # Run tests (if available)
    - name: Run tests
      run: npm test --if-present
      continue-on-error: false
    
    # Build the project
    - name: Build project
      run: npm run build
    
    # Verify build output exists
    - name: Verify build output
      run: |
        if [ ! -d ".next" ] && [ ! -d "dist" ] && [ ! -d "build" ]; then
          echo "Build output directory not found!"
          exit 1
        fi
        echo "Build completed successfully!"
    
    # Create backup branch only if build succeeds
    - name: Create backup branch
      if: success() && github.ref == 'refs/heads/main'
      run: |
        # Configure git
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # Generate backup branch name with timestamp
        TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
        BACKUP_BRANCH="${{ env.BACKUP_BRANCH_PREFIX }}/successful-build-${TIMESTAMP}"
        
        # Create and push backup branch
        git checkout -b "$BACKUP_BRANCH"
        git push origin "$BACKUP_BRANCH"
        
        echo "✅ Backup branch created: $BACKUP_BRANCH"
        echo "backup-branch=$BACKUP_BRANCH" >> $GITHUB_OUTPUT
    
    # Optional: Clean up old backup branches (keep last 10)
    - name: Clean up old backup branches
      if: success() && github.ref == 'refs/heads/main'
      run: |
        # Get all backup branches sorted by creation date (oldest first)
        BACKUP_BRANCHES=$(git branch -r | grep "origin/${{ env.BACKUP_BRANCH_PREFIX }}/" | sed 's/origin\///' | sort)
        BRANCH_COUNT=$(echo "$BACKUP_BRANCHES" | wc -l)
        
        # Keep only the latest 10 backup branches
        if [ $BRANCH_COUNT -gt 10 ]; then
          BRANCHES_TO_DELETE=$(echo "$BACKUP_BRANCHES" | head -n $((BRANCH_COUNT - 10)))
          
          for branch in $BRANCHES_TO_DELETE; do
            echo "Deleting old backup branch: $branch"
            git push origin --delete "$branch" || echo "Failed to delete $branch"
          done
        fi
    
    # Post build summary
    - name: Build Summary
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "🎉 Build completed successfully!"
          echo "📦 Project built and backup branch created"
        else
          echo "❌ Build failed - no backup branch created"
        fi

  # Optional: Notify on success/failure
  notify:
    runs-on: ubuntu-latest
    needs: build-and-backup
    if: always()
    
    steps:
    - name: Notify build result
      run: |
        if [ "${{ needs.build-and-backup.result }}" == "success" ]; then
          echo "✅ Build successful - Backup branch created"
        else
          echo "❌ Build failed - No backup created"
        fi