import * as XLSX from "xlsx"
import { SHEET_CONFIGS } from "./constants"

// Types for Web Worker communication
interface WorkerMessage {
  type: 'PARSE_EXCEL'
  payload: {
    fileBuffer: ArrayBuffer
    fileName: string
  }
}

interface WorkerResponse {
  type: 'PARSE_SUCCESS' | 'PARSE_ERROR' | 'PARSE_PROGRESS'
  payload: any
}

/**
 * Parse Excel file using Web Worker for better performance
 * Falls back to main thread parsing if Web Worker is not available
 */
export async function parseExcelFileWithWorker(
  file: File,
  onProgress?: (progress: number, message: string) => void
): Promise<{ [key: string]: any[] }> {
  // Check if Web Workers are supported
  if (typeof Worker === 'undefined') {
    console.warn('Web Workers not supported, falling back to main thread parsing')
    return parseExcelFile(file)
  }

  return new Promise((resolve, reject) => {
    try {
      // Create Web Worker
      const worker = new Worker(new URL('./excelWorker.ts', import.meta.url))
      
      // Set up message handler
      worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
        const { type, payload } = event.data
        
        switch (type) {
          case 'PARSE_SUCCESS':
            worker.terminate()
            resolve(payload)
            break
            
          case 'PARSE_ERROR':
            worker.terminate()
            reject(new Error(payload.error))
            break
            
          case 'PARSE_PROGRESS':
            if (onProgress) {
              onProgress(payload.progress, payload.message)
            }
            break
        }
      }
      
      // Handle worker errors
      worker.onerror = (error) => {
        worker.terminate()
        console.error('Worker error:', error)
        reject(new Error('Excel parsing failed in worker thread'))
      }
      
      // Convert file to ArrayBuffer and send to worker
      const reader = new FileReader()
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer
        worker.postMessage({
          type: 'PARSE_EXCEL',
          payload: {
            fileBuffer: arrayBuffer,
            fileName: file.name
          }
        } as WorkerMessage)
      }
      
      reader.onerror = () => {
        worker.terminate()
        reject(new Error('Failed to read file'))
      }
      
      reader.readAsArrayBuffer(file)
      
    } catch (error) {
      console.error('Error setting up worker:', error)
      // Fall back to main thread parsing
      resolve(parseExcelFile(file))
    }
  })
}

export async function parseExcelFile(file: File): Promise<any> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: "array" })

        const parsedData: any = {}

        SHEET_CONFIGS.forEach((config) => {
          const worksheet = workbook.Sheets[config.name]
          if (worksheet) {
            let jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

            // Handle dynamic header detection for 'Data base' sheet
            if (config.key === "database") {
              jsonData = handleDatabaseSheet(jsonData as any[][])
            } else {
              // Standard processing for other sheets
              jsonData = handleStandardSheet(jsonData as any[][])
            }

            parsedData[config.key] = jsonData
          }
        })

        resolve(parsedData)
      } catch (error) {
        reject(new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : "Unknown error"}`))
      }
    }

    reader.onerror = () => {
      reject(new Error("Failed to read file"))
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * Handles the complex structure of the Database sheet as documented in Data base.md
 * The sheet has a multi-row header structure with merged cells and container objects
 */
function handleDatabaseSheet(data: any[][]): any[] {
  if (!data || data.length < 4) return []

  // According to Data base.md, the structure is:
  // Row 1: Main headers (A=# B=Company C=Company code, etc.)
  // Row 2: Container headers (D=Weekly update, etc.)
  // Row 3: Sub-headers for containers (Current market price, etc.)
  // Row 4+: Data rows

  const headers: string[] = []
  // Assuming data[0] and data[1] are the relevant header rows as per user's description.
  // data[2] might also be used by existing logic, let's keep it for now.
  const headerRow1 = data[0] || []
  const headerRow2 = data[1] || []
  const headerRow3 = data[2] || [] // Existing logic uses this

  // Determine the maximum number of columns from the header rows and first few data rows
  const numDataRowsToCheck = Math.min(data.length, 10) // Check first 10 data rows for max columns
  const relevantRowsForMaxCols = data.slice(0, numDataRowsToCheck)
  const maxCols = Math.max(...relevantRowsForMaxCols.map(row => row?.length || 0))


  // Specific mappings provided by the user
  const specificHeaderMappings: { [key: number]: string } = {
    112: "Net asset per share 2024", // Column_113
    131: "EPS 2024",                 // Column_132
    145: "Cash Dividend 2024",       // Column_146
  };

  for (let col = 0; col < maxCols; col++) {
    let finalHeader = ''

    // Check specific mappings first
    if (specificHeaderMappings[col]) {
      finalHeader = specificHeaderMappings[col]
    } else {
      const row1Header = headerRow1[col] ? String(headerRow1[col]).trim() : ''
      const row2Header = headerRow2[col] ? String(headerRow2[col]).trim() : ''
      const row3Header = headerRow3[col] ? String(headerRow3[col]).trim() : '' // From original logic

      // This part replicates the original logic for combining headers,
      // prioritizing combinations of row1 and row2 as per "first two objects"
      if (col === 0) {
        finalHeader = '#'
      } else if (col === 1) {
        finalHeader = 'Company'
      } else if (col === 2) {
        finalHeader = 'Company code'
      } else if (col === 3 && row3Header) { // Original logic used row3Header here
        finalHeader = 'Current price' // Assuming 'Current market price' from row3
      } else if (col === 4 && row3Header) { // Original logic used row3Header here
        finalHeader = 'Total Market Cap'
      } else if (col === 5 && row3Header) { // Original logic used row3Header here
        finalHeader = 'BETA VALUES AGAINST ASPI'
      }
      // Preferred combination: Row 1 + Row 2 if both exist
      else if (row1Header && row2Header) {
        // If row2Header is a year (e.g., "2024"), and row1Header is a metric name.
        if (/^\d{4}$/.test(row2Header)) {
          finalHeader = `${row1Header} ${row2Header}`
        } else {
          // General combination
          finalHeader = `${row1Header} ${row2Header}`.trim() // Or `${row2Header} ${row1Header}` depending on sheet structure
        }
      }
      // Fallback to row1 if row2 is empty
      else if (row1Header) {
        finalHeader = row1Header
      }
      // Fallback to row2 if row1 is empty (less likely for a primary header)
      else if (row2Header) {
        finalHeader = row2Header
      }
      // Fallback to row3 if others are empty (from original logic)
      else if (row3Header) {
        finalHeader = row3Header
      }
      // Ultimate fallback if no header information is found in first three rows
      else {
        finalHeader = `Column_${col + 1}`
      }
    }
    headers.push(finalHeader.trim())
  }

  // Process data rows (starting from row 4, index 3, as per original logic)
  const dataRows = data.slice(3)

  return dataRows
    .filter((row) => {
      // Filter out completely empty rows
      return row && row.some((cell) => {
        if (cell === null || cell === undefined || cell === '') return false
        // Filter out Excel error values
        const cellStr = String(cell).trim()
        return cellStr !== '' && cellStr !== '#REF!' && cellStr !== '#VALUE!' && cellStr !== '#DIV/0!' && cellStr !== '#N/A'
      })
    })
    .map((row) => {
      const obj: any = {}
      headers.forEach((header, index) => {
    let originalValue = row[index]
    let processedValue: any = null // Default to null for empty/undefined original values

    if (originalValue !== null && originalValue !== undefined && String(originalValue).trim() !== '') {
      const valueStr = String(originalValue).trim()

      // Handle specific textual representations for missing/invalid data
      // Make checks case-insensitive for robustness
      const upperValueStr = valueStr.toUpperCase()
      if (['#REF!', '#VALUE!', '#DIV/0!', '#N/A'].includes(upperValueStr)) {
        processedValue = null
      } else if (valueStr.toLowerCase() === "not mention") {
        processedValue = null // Standardize "not mention" to null
      } else {
        // Attempt to parse as a number
        // 1. Remove thousands separators (commas) that are not decimal points.
        //    JavaScript's parseFloat handles decimal points correctly.
        //    We are removing commas used as thousands separators.
        let numericString = valueStr.replace(/,(?=\d{3})/g, '').trim() // Remove commas used as thousands separators

        const isPercentage = numericString.endsWith('%')
        if (isPercentage) {
          numericString = numericString.slice(0, -1).trim() // Remove % sign
        }

        // Check if the string purely represents a number (possibly with decimal or negative)
        // Allows: -123, 123, 123.45, .5, -.5
        if (/^-?\d*\.?\d+$/.test(numericString) || /^-?\d+\.?\d*$/.test(numericString)) {
          let numValue = parseFloat(numericString)
          if (!isNaN(numValue)) {
            if (isPercentage) {
              processedValue = numValue / 100 // Store percentages as decimal values (e.g., 50% as 0.5)
            } else {
              processedValue = numValue
            }
          } else {
            // If parseFloat fails after cleaning, it's not a standard number.
            processedValue = valueStr // Fallback to original trimmed string value
          }
        } else {
          // If not purely numeric (e.g., contains other text, or is just "-"), keep original trimmed string.
          processedValue = valueStr
        }
      }
    }
    obj[header] = processedValue
  })
  return obj
    })
}

function handleStandardSheet(data: any[][]): any[] {
  if (data.length < 2) return []

  const headers = data[0].map((header, index) =>
    header && String(header).trim() !== "" ? String(header).trim() : `Column_${index + 1}`,
  )

  const rows = data.slice(1)

  return rows
    .filter((row) => row && row.some((cell) => cell !== null && cell !== undefined && cell !== ""))
    .map((row) => {
      const obj: any = {}
      headers.forEach((header, index) => {
        obj[header] = row[index] !== null && row[index] !== undefined ? row[index] : ""
      })
      return obj
    })
}

export function formatNumber(value: any): string {
  if (value === "N/A") return "N/A";
  if (value === null || value === undefined || value === "") return "-"

  const num = Number.parseFloat(String(value).replace(/[^\d.-]/g, ""))
  if (isNaN(num)) return String(value)

  return num.toLocaleString()
}

export function formatPercentage(value: any): string {
  if (value === null || value === undefined || value === "") return "-"

  const num = Number.parseFloat(String(value).replace(/[^\d.-]/g, ""))
  if (isNaN(num)) return String(value)

  return `${num.toFixed(2)}%`
}
