export interface DatabaseRecord {
  filename: string
  date: string
  data: {
    database: any[]
  }
}

// Client-side function to load all database records via API
export async function loadAllDatabaseRecordsClient(limit: number = 30): Promise<DatabaseRecord[]> {
  try {
    const url = `/api/database-records${limit ? `?limit=${limit}` : ''}`
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Failed to fetch database records')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading database records from API:', error)
    return []
  }
}