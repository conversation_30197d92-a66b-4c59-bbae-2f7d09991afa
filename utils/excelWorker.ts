/**
 * Web Worker for Excel file parsing to improve performance
 * Handles large Excel files in a separate thread to prevent UI blocking
 */

import * as XLSX from 'xlsx'
import { SHEET_CONFIGS } from './constants'

// Define types for worker communication
interface WorkerMessage {
  type: 'PARSE_EXCEL'
  payload: {
    fileBuffer: ArrayBuffer
    fileName: string
  }
}

interface WorkerResponse {
  type: 'PARSE_SUCCESS' | 'PARSE_ERROR' | 'PARSE_PROGRESS'
  payload: any
}

/**
 * Handle database sheet with complex multi-row headers
 */
function handleDatabaseSheet(worksheet: XLSX.WorkSheet): any[] {
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
  const data: any[] = []
  const headers: string[] = []
  const maxCols = range.e.c // Max columns from sheet range

  // Specific mappings (0-indexed column)
  const specificHeaderMappings: { [key: number]: string } = {
    112: "Net asset per share 2024", // Column_113
    131: "EPS 2024",                 // Column_132
    145: "Cash Dividend 2024",       // Column_146
  };

  for (let col = range.s.c; col <= maxCols; col++) {
    let finalHeader = ''
    const colLetter = XLSX.utils.encode_col(col)

    // Check specific mappings first (col is 0-indexed)
    if (specificHeaderMappings[col]) {
      finalHeader = specificHeaderMappings[col]
    } else {
      const row1Value = worksheet[`${colLetter}1`]?.v
      const row2Value = worksheet[`${colLetter}2`]?.v
      const row3Value = worksheet[`${colLetter}3`]?.v

      const row1Header = row1Value ? String(row1Value).trim() : ''
      const row2Header = row2Value ? String(row2Value).trim() : ''
      const row3Header = row3Value ? String(row3Value).trim() : ''

      if (col === 0) {
        finalHeader = '#'
      } else if (col === 1) {
        finalHeader = 'Company'
      } else if (col === 2) {
        finalHeader = 'Company code'
      } else if (col === 3 && row3Header) {
        finalHeader = 'Current price'
      } else if (col === 4 && row3Header) {
        finalHeader = 'Total Market Cap'
      } else if (col === 5 && row3Header) {
        finalHeader = 'BETA VALUES AGAINST ASPI'
      } else if (row1Header && row2Header) {
        if (/^\d{4}$/.test(row2Header)) {
          finalHeader = `${row1Header} ${row2Header}`
        } else {
          finalHeader = `${row1Header} ${row2Header}`.trim()
        }
      } else if (row1Header) {
        finalHeader = row1Header
      } else if (row2Header) {
        finalHeader = row2Header
      } else if (row3Header) {
        finalHeader = row3Header
      } else {
        finalHeader = `Column_${col + 1}`
      }
    }
    headers.push(finalHeader.trim())
  }
  
  // Process data rows starting from row 4 (0-indexed '3' for worksheet access, XLSX uses 1-based for cell addresses like A1)
  // The actual data content starts from the 4th row of the sheet, which is index 3.
  for (let r = 3; r <= range.e.r; r++) { // r is 0-indexed row number for worksheet access
    const rowData: any = {}
    let hasData = false
    
    // In the loop: for (let r = 3; r <= range.e.r; r++)
    // headers is 0-indexed based on column number from range.s.c to range.e.c
    for (let c = range.s.c; c <= maxCols; c++) { // Use maxCols consistent with header generation
      const headerIndex = c - range.s.c; // Calculate 0-based index for headers array
      const header = headers[headerIndex];
      if (header === undefined) continue; // Should not happen if maxCols is derived from range.e.c and headers is built up to maxCols

      const cellAddress = XLSX.utils.encode_cell({ r: r, c: c }) // r is current row, c is current col
      const cell = worksheet[cellAddress]
      const originalValue = cell?.v // Raw value from the cell

      let processedValue: any = null // Default to null

      if (originalValue !== null && originalValue !== undefined && String(originalValue).trim() !== '') {
        const valueStr = String(originalValue).trim()
        const upperValueStr = valueStr.toUpperCase()

        if (['#REF!', '#VALUE!', '#DIV/0!', '#N/A', '#NAME?'].includes(upperValueStr)) { // Added #NAME?
          processedValue = null
        } else if (valueStr.toLowerCase() === "not mention") {
          processedValue = null
        } else {
          let numericString = valueStr.replace(/,(?=\d{3})/g, '').trim()
          const isPercentage = numericString.endsWith('%')
          if (isPercentage) {
            numericString = numericString.slice(0, -1).trim()
          }

          if (/^-?\d*\.?\d+$/.test(numericString) || /^-?\d+\.?\d*$/.test(numericString)) {
            let numValue = parseFloat(numericString)
            if (!isNaN(numValue)) {
              if (isPercentage) {
                processedValue = numValue / 100
              } else {
                processedValue = numValue
              }
            } else {
              processedValue = valueStr
            }
          } else {
            processedValue = valueStr
          }
        }
      }

      rowData[header] = processedValue
      if (processedValue !== null && processedValue !== undefined && processedValue !== '') {
        hasData = true
      }
    }
    
    // Only add rows that have some data
    if (hasData) {
      data.push(rowData)
    }
  }
  
  return data
}

/**
 * Handle standard sheets with simple headers
 */
function handleStandardSheet(worksheet: XLSX.WorkSheet): any[] {
  try {
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1,
      defval: null,
      raw: false
    })
    
    if (jsonData.length === 0) return []
    
    const headers = jsonData[0] as string[]
    const data: any[] = []
    
    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i] as any[]
      const rowData: any = {}
      let hasData = false
      
      headers.forEach((header, index) => {
        let value = row[index]
        
        // Filter out Excel error values
        if (typeof value === 'string' && 
            (value.includes('#REF!') || value.includes('#VALUE!') || 
             value.includes('#NAME?') || value.includes('#DIV/0!'))) {
          value = null
        } else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
          // Try to parse as number if it looks numeric
          const numValue = Number(value)
          if (!isNaN(numValue)) {
            value = numValue
          }
        }
        
        rowData[header || `Column_${index + 1}`] = value
        if (value !== null && value !== undefined && value !== '') {
          hasData = true
        }
      })
      
      if (hasData) {
        data.push(rowData)
      }
    }
    
    return data
  } catch (error) {
    console.error('Error processing standard sheet:', error)
    return []
  }
}

/**
 * Main Excel parsing function
 */
function parseExcelFile(fileBuffer: ArrayBuffer, fileName: string) {
  try {
    // Send progress update
    self.postMessage({
      type: 'PARSE_PROGRESS',
      payload: { progress: 10, message: 'Reading Excel file...' }
    } as WorkerResponse)
    
    const workbook = XLSX.read(fileBuffer, { type: 'array' })
    const result: { [key: string]: any[] } = {}
    const totalSheets = SHEET_CONFIGS.length
    
    // Send progress update
    self.postMessage({
      type: 'PARSE_PROGRESS',
      payload: { progress: 30, message: 'Processing sheets...' }
    } as WorkerResponse)
    
    SHEET_CONFIGS.forEach((config, index) => {
      try {
        const worksheet = workbook.Sheets[config.name]
        if (worksheet) {
          let sheetData: any[]
          
          if (config.key === 'database') {
            sheetData = handleDatabaseSheet(worksheet)
          } else {
            sheetData = handleStandardSheet(worksheet)
          }
          
          result[config.key] = sheetData
          
          // Send progress update for each sheet
          const progress = 30 + ((index + 1) / totalSheets) * 60
          self.postMessage({
            type: 'PARSE_PROGRESS',
            payload: { 
              progress: Math.round(progress), 
              message: `Processed ${config.name} sheet...` 
            }
          } as WorkerResponse)
        } else {
          console.warn(`Sheet "${config.name}" not found in ${fileName}`)
          result[config.key] = []
        }
      } catch (error) {
        console.error(`Error processing sheet "${config.name}":`, error)
        result[config.key] = []
      }
    })
    
    // Send final progress
    self.postMessage({
      type: 'PARSE_PROGRESS',
      payload: { progress: 100, message: 'Parsing complete!' }
    } as WorkerResponse)
    
    return result
  } catch (error) {
    throw new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Worker message handler
self.onmessage = function(event: MessageEvent<WorkerMessage>) {
  const { type, payload } = event.data
  
  try {
    switch (type) {
      case 'PARSE_EXCEL':
        const result = parseExcelFile(payload.fileBuffer, payload.fileName)
        self.postMessage({
          type: 'PARSE_SUCCESS',
          payload: result
        } as WorkerResponse)
        break
        
      default:
        throw new Error(`Unknown message type: ${type}`)
    }
  } catch (error) {
    self.postMessage({
      type: 'PARSE_ERROR',
      payload: {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      }
    } as WorkerResponse)
  }
}

// Export for TypeScript (won't be used in worker context)
export {}