export interface DatabaseRecord {
  filename: string
  date: string
  data: {
    database: any[]
  }
}

// Extract date from filename (format: "Stock mareket Database 2025 DD-MM-YYYY.xlsx")
function extractDateFromFilename(filename: string): string {
  const match = filename.match(/(\d{2}-\d{2}-\d{4})/)
  if (match) {
    const [day, month, year] = match[1].split('-')
    return `${year}-${month}-${day}` // Convert to YYYY-MM-DD format for sorting
  }
  return filename // Fallback to filename if date extraction fails
}

// Server-side function to load all database records from the data directory
export async function loadAllDatabaseRecords(limit: number = 30): Promise<DatabaseRecord[]> {
  // This function is only used server-side in the API route
  const fs = await import('fs')
  const path = await import('path')
  
  try {
    const dataDir = path.join(process.cwd(), 'data-sampling')
    
    // Check if data directory exists
    if (!fs.existsSync(dataDir)) {
      console.warn('Data directory not found:', dataDir)
      return []
    }
    
    const files = fs.readdirSync(dataDir)
    const jsonFiles = files.filter(file => file.endsWith('.json'))
    
    const records: DatabaseRecord[] = []
    
    for (const file of jsonFiles) {
      try {
        const filePath = path.join(dataDir, file)
        const fileContent = fs.readFileSync(filePath, 'utf-8')
        const jsonData = JSON.parse(fileContent)
        
        if (jsonData.data && jsonData.data.database) {
          records.push({
            filename: jsonData.filename || file,
            date: extractDateFromFilename(jsonData.filename || file),
            data: {
              database: jsonData.data.database
            }
          })
        }
      } catch (error) {
        console.error(`Error loading file ${file}:`, error)
      }
    }
    
    // Sort by date (most recent first) and limit to specified number of documents
    const sortedRecords = records
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit)
    
    console.log(`Loaded ${sortedRecords.length} database records (limit: ${limit})${sortedRecords.length > 0 ? ` from ${sortedRecords[sortedRecords.length - 1].date} to ${sortedRecords[0].date}` : ''}`)
    
    return sortedRecords
  } catch (error) {
    console.error('Error loading database records:', error)
    return []
  }
}

// Client-side function to load all database records via API
export async function loadAllDatabaseRecordsClient(): Promise<DatabaseRecord[]> {
  try {
    const response = await fetch('/api/database-records')
    if (!response.ok) {
      throw new Error('Failed to fetch database records')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading database records from API:', error)
    return []
  }
}