import { MongoClient, Db, Collection } from 'mongodb'

// Don't throw error immediately - let individual functions handle it
const MONGODB_CONNECTION_STRING = process.env.MONGODBCONNECTION

// MongoDB connection configured

let client: MongoClient
let clientPromise: Promise<MongoClient> | null = null

function initializeConnection() {
  if (!MONGODB_CONNECTION_STRING) {
    throw new Error('MongoDB connection string not configured. Please add MONG<PERSON>BCONNECTION to your .env.local file.')
  }

  const options = {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 10000,
    connectTimeoutMS: 10000,
    maxIdleTimeMS: 30000,
    retryWrites: true,
    w: 'majority'
  }

  if (process.env.NODE_ENV === 'development') {
    // In development mode, use a global variable so that the value
    // is preserved across module reloads caused by HMR (Hot Module Replacement).
    let globalWithMongo = global as typeof globalThis & {
      _mongoClientPromise?: Promise<MongoClient>
    }

    if (!globalWithMongo._mongoClientPromise) {
      client = new MongoClient(MONGODB_CONNECTION_STRING, options)
      globalWithMongo._mongoClientPromise = client.connect()
    }
    clientPromise = globalWithMongo._mongoClientPromise
  } else {
    // In production mode, it's best to not use a global variable.
    client = new MongoClient(MONGODB_CONNECTION_STRING, options)
    clientPromise = client.connect()
  }

  return clientPromise
}

/**
 * Get MongoDB database instance
 * @param dbName - Database name (default: 'exceldashboard')
 * @returns Database instance
 */
export async function getDatabase(dbName: string = 'exceldashboard'): Promise<Db> {
  try {
    if (!clientPromise) {
      clientPromise = initializeConnection()
    }
    const client = await clientPromise
    return client.db(dbName)
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error)
    throw new Error(`MongoDB connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Ensure indexes are created for better performance
 */
async function ensureIndexes() {
  try {
    const collection = await getCollection('excel_uploads')

    // Create index on uploadedAt for sorting
    await collection.createIndex({ uploadedAt: -1 })

    // Create index on data.database existence for filtering
    await collection.createIndex({ 'data.database': 1 })

    console.log('Database indexes ensured')
  } catch (error) {
    console.error('Error creating indexes:', error)
    // Don't throw error - indexes are optional for functionality
  }
}

/**
 * Get a specific collection from the database
 * @param collectionName - Name of the collection
 * @param dbName - Database name (default: 'exceldashboard')
 * @returns Collection instance
 */
export async function getCollection<T = any>(
  collectionName: string,
  dbName: string = 'exceldashboard'
): Promise<Collection<T>> {
  try {
    const db = await getDatabase(dbName)
    const collection = db.collection<T>(collectionName)

    // Ensure indexes on first access to excel_uploads collection
    if (collectionName === 'excel_uploads') {
      ensureIndexes().catch(console.error)
    }

    return collection
  } catch (error) {
    console.error(`Failed to get collection ${collectionName}:`, error)
    throw error
  }
}

/**
 * Save Excel data to MongoDB
 * @param data - Parsed Excel data
 * @param filename - Original filename
 * @param uploadedBy - User identifier (optional)
 * @returns Saved document with ID
 */
export async function saveExcelData(
  data: any,
  filename: string,
  uploadedBy?: string
) {
  try {
    const collection = await getCollection('excel_uploads')

    const document = {
      filename,
      data,
      uploadedBy: uploadedBy || 'anonymous',
      uploadedAt: new Date(),
      metadata: {
        sheets: Object.keys(data || {}),
        totalRecords: Object.values(data || {}).reduce(
          (total: number, sheet: any) => total + (Array.isArray(sheet) ? sheet.length : 0),
          0
        )
      }
    }

    const result = await collection.insertOne(document)
    return { ...document, _id: result.insertedId }
  } catch (error) {
    console.error('Error saving Excel data to MongoDB:', error)
    throw new Error(`Failed to save data: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get all uploaded Excel files
 * @param limit - Maximum number of records to return
 * @param skip - Number of records to skip
 * @returns Array of uploaded files
 */
export async function getUploadedFiles(limit: number = 10, skip: number = 0) {
  try {
    const collection = await getCollection('excel_uploads')

    // Limit the number of documents and use minimal projection
    const safeLimit = Math.min(limit, 20) // Cap at 20 documents max

    // Use projection to only fetch necessary fields for better performance
    const documents = await collection
      .find({}, {
        projection: {
          filename: 1,
          uploadedAt: 1,
          uploadedBy: 1,
          metadata: 1
        }
      })
      .sort({ uploadedAt: -1 })
      .limit(safeLimit)
      .skip(skip)
      .toArray()

    console.log(`Fetched ${documents.length} file metadata records`)

    // Transform documents to ensure proper serialization
    return documents.map(doc => ({
      _id: doc._id?.toString() || '',
      filename: doc.filename || 'Unknown',
      uploadedAt: doc.uploadedAt?.toISOString() || new Date().toISOString(),
      uploadedBy: doc.uploadedBy || 'anonymous',
      metadata: doc.metadata || {}
    }))
  } catch (error) {
    console.error('Error fetching uploaded files:', error)
    throw new Error(`Failed to fetch files: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get recent database records for pricing trends analysis
 * @param limit - Maximum number of records to return (default: 15)
 * @returns Array of database records with pricing data
 */
export async function getRecentDatabaseRecords(limit: number = 15) {
  try {
    const collection = await getCollection('excel_uploads')

    // Limit the number of records and use safer limits
    const safeLimit = Math.min(limit, 10) // Cap at 10 records to prevent memory issues

    console.log(`Fetching ${safeLimit} recent database records...`)

    // Use projection to only fetch necessary fields and improve performance
    // Limit database array size to prevent memory issues
    const records = await collection
      .find(
        {
          'data.database': { $exists: true, $ne: null }
        },
        {
          projection: {
            filename: 1,
            uploadedAt: 1,
            'data.database': { $slice: 100 } // Limit to first 100 database records per document
          }
        }
      )
      .sort({ uploadedAt: -1 })
      .limit(safeLimit)
      .toArray()

    console.log(`Found ${records.length} records with database data`)

    // Transform the data to match the expected format
    return records.map(record => {
      const databaseArray = Array.isArray(record.data?.database) ? record.data.database : []
      console.log(`Record ${record.filename}: ${databaseArray.length} database entries`)

      return {
        _id: record._id?.toString() || '',
        filename: record.filename || 'Unknown',
        date: record.uploadedAt ? record.uploadedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        uploadedAt: record.uploadedAt ? record.uploadedAt.toISOString() : new Date().toISOString(),
        data: {
          database: databaseArray
        }
      }
    })
  } catch (error) {
    console.error('Error in getRecentDatabaseRecords:', error)
    throw new Error(`Failed to fetch recent records: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get a specific uploaded file by ID
 * @param id - Document ID
 * @returns Uploaded file document
 */
export async function getUploadedFileById(id: string) {
  try {
    const collection = await getCollection('excel_uploads')
    const { ObjectId } = require('mongodb')

    const document = await collection.findOne({ _id: new ObjectId(id) })

    if (!document) {
      return null
    }

    // Transform document to ensure proper serialization
    return {
      ...document,
      _id: document._id?.toString() || '',
      uploadedAt: document.uploadedAt?.toISOString() || new Date().toISOString()
    }
  } catch (error) {
    console.error('Error fetching file by ID:', error)
    throw new Error(`Failed to fetch file: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Delete an uploaded file by ID
 * @param id - Document ID
 * @returns Deletion result
 */
export async function deleteUploadedFile(id: string) {
  try {
    const collection = await getCollection('excel_uploads')
    const { ObjectId } = require('mongodb')

    return await collection.deleteOne({ _id: new ObjectId(id) })
  } catch (error) {
    console.error('Error deleting file:', error)
    throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Export the client promise for backward compatibility
export default async function getClientPromise() {
  if (!clientPromise) {
    clientPromise = initializeConnection()
  }
  return clientPromise
}